#include "SystemAPITest.h"
#include <QDateTime>

SystemAPITest::SystemAPITest(QObject *parent)
    : QObject(parent)
    , m_testTimer(new QTimer(this))
    , m_testCount(0)
    , m_successCount(0)
    , m_totalEDIDFound(0)
    , m_running(false)
{
    // 设置1秒间隔的定时器
    m_testTimer->setInterval(1000);
    connect(m_testTimer, &QTimer::timeout, this, &SystemAPITest::performPeriodicTest);
}

SystemAPITest::~SystemAPITest()
{
    stopTest();
}

void SystemAPITest::startTest()
{
    if (m_running) {
        return;
    }
    
    qDebug() << "=== 开始系统API EDID检测测试 ===";
    qDebug() << "测试间隔: 1秒";
    qDebug() << "测试时间:" << QDateTime::currentDateTime().toString();
    
    m_testCount = 0;
    m_successCount = 0;
    m_totalEDIDFound = 0;
    m_running = true;
    
    // 先执行一次完整测试
    runFullTest();
    
    // 启动定时测试
    m_testTimer->start();
}

void SystemAPITest::stopTest()
{
    if (!m_running) {
        return;
    }
    
    m_testTimer->stop();
    m_running = false;
    
    outputTestStatistics();
    qDebug() << "=== 系统API测试结束 ===";
}

void SystemAPITest::runFullTest()
{
    qDebug() << "\n--- 执行完整API测试 ---";
    
    testSetupAPI();
    testDisplayAPI();
    testRegistryMethod();
    testEDIDParsing();
    
    qDebug() << "--- 完整测试结束 ---\n";
}

void SystemAPITest::performPeriodicTest()
{
    m_testCount++;
    
    qDebug() << QString("=== 第 %1 次定时检测 ===").arg(m_testCount);
    
    // 获取所有连接的显示器
    QList<EDIDParser::EDIDInfo> monitors = EDIDParser::getAllConnectedMonitors();
    
    if (!monitors.isEmpty()) {
        m_successCount++;
        m_totalEDIDFound += monitors.size();
        
        qDebug() << QString("检测成功！发现 %1 个显示器:").arg(monitors.size());
        
        foreach (const EDIDParser::EDIDInfo& monitor, monitors) {
            qDebug() << QString("  - %1 (ID: %2)")
                        .arg(monitor.getDisplayString())
                        .arg(monitor.uniqueId);
        }
        
        emit edidDetected("综合检测", monitors.size(), 
                         QString("成功检测到 %1 个显示器").arg(monitors.size()));
    } else {
        qDebug() << "未检测到显示器";
        emit edidDetected("综合检测", 0, "未检测到显示器");
    }
    
    // 每10次输出统计信息
    if (m_testCount % 10 == 0) {
        outputTestStatistics();
    }
}

void SystemAPITest::testSetupAPI()
{
    qDebug() << "测试SetupAPI方法...";
    
    try {
        QList<QByteArray> edidList = EDIDParser::getEDIDFromSetupAPI();
        
        if (!edidList.isEmpty()) {
            qDebug() << QString("SetupAPI成功: 获取到 %1 个EDID数据").arg(edidList.size());
            emit testResult("SetupAPI", true, 
                           QString("获取到 %1 个EDID数据").arg(edidList.size()));
        } else {
            qDebug() << "SetupAPI: 未获取到EDID数据";
            emit testResult("SetupAPI", false, "未获取到EDID数据");
        }
    } catch (const std::exception& e) {
        qDebug() << "SetupAPI异常:" << e.what();
        emit testResult("SetupAPI", false, QString("异常: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "SetupAPI未知异常";
        emit testResult("SetupAPI", false, "未知异常");
    }
}

void SystemAPITest::testDisplayAPI()
{
    qDebug() << "测试DisplayAPI方法...";
    
    try {
        QList<QByteArray> edidList = EDIDParser::getEDIDFromDisplayAPI();
        
        if (!edidList.isEmpty()) {
            qDebug() << QString("DisplayAPI成功: 获取到 %1 个EDID数据").arg(edidList.size());
            emit testResult("DisplayAPI", true, 
                           QString("获取到 %1 个EDID数据").arg(edidList.size()));
        } else {
            qDebug() << "DisplayAPI: 未获取到EDID数据";
            emit testResult("DisplayAPI", false, "未获取到EDID数据");
        }
    } catch (const std::exception& e) {
        qDebug() << "DisplayAPI异常:" << e.what();
        emit testResult("DisplayAPI", false, QString("异常: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "DisplayAPI未知异常";
        emit testResult("DisplayAPI", false, "未知异常");
    }
}

void SystemAPITest::testRegistryMethod()
{
    qDebug() << "测试注册表方法...";
    
    try {
        QList<QByteArray> edidList = EDIDParser::getEDIDFromRegistry();
        
        if (!edidList.isEmpty()) {
            qDebug() << QString("注册表方法成功: 获取到 %1 个EDID数据").arg(edidList.size());
            emit testResult("Registry", true, 
                           QString("获取到 %1 个EDID数据").arg(edidList.size()));
        } else {
            qDebug() << "注册表方法: 未获取到EDID数据";
            emit testResult("Registry", false, "未获取到EDID数据");
        }
    } catch (const std::exception& e) {
        qDebug() << "注册表方法异常:" << e.what();
        emit testResult("Registry", false, QString("异常: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "注册表方法未知异常";
        emit testResult("Registry", false, "未知异常");
    }
}

void SystemAPITest::testEDIDParsing()
{
    qDebug() << "测试EDID解析...";
    
    try {
        QList<EDIDParser::EDIDInfo> monitors = EDIDParser::getAllConnectedMonitors();
        
        if (!monitors.isEmpty()) {
            qDebug() << QString("EDID解析成功: 解析了 %1 个显示器").arg(monitors.size());
            
            foreach (const EDIDParser::EDIDInfo& monitor, monitors) {
                qDebug() << QString("  显示器: %1").arg(monitor.getDisplayString());
                qDebug() << QString("    制造商: %1").arg(monitor.manufacturer);
                qDebug() << QString("    型号: %1").arg(monitor.modelName);
                qDebug() << QString("    唯一标识: %1").arg(monitor.uniqueId);
                qDebug() << QString("    尺寸: %1x%2mm").arg(monitor.width).arg(monitor.height);
            }
            
            emit testResult("EDID解析", true, 
                           QString("成功解析 %1 个显示器").arg(monitors.size()));
        } else {
            qDebug() << "EDID解析: 未解析到显示器";
            emit testResult("EDID解析", false, "未解析到显示器");
        }
    } catch (const std::exception& e) {
        qDebug() << "EDID解析异常:" << e.what();
        emit testResult("EDID解析", false, QString("异常: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "EDID解析未知异常";
        emit testResult("EDID解析", false, "未知异常");
    }
}

void SystemAPITest::outputTestStatistics()
{
    qDebug() << "\n=== 测试统计信息 ===";
    qDebug() << QString("总测试次数: %1").arg(m_testCount);
    qDebug() << QString("成功次数: %1").arg(m_successCount);
    qDebug() << QString("成功率: %1%").arg(m_testCount > 0 ? (m_successCount * 100.0 / m_testCount) : 0);
    qDebug() << QString("总检测到EDID数: %1").arg(m_totalEDIDFound);
    qDebug() << QString("平均每次检测: %1").arg(m_successCount > 0 ? (m_totalEDIDFound * 1.0 / m_successCount) : 0);
    qDebug() << "==================\n";
}
