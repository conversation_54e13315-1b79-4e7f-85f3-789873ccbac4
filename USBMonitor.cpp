#include "USBMonitor.h"
#include <QDebug>
#include <QDir>
#include <QStorageInfo>
#include <QApplication>

#ifdef Q_OS_WIN
#include <windows.h>
#include <dbt.h>
#include <setupapi.h>
#include <cfgmgr32.h>
#include <winioctl.h>
#endif

USBMonitor::USBMonitor(QObject *parent)
    : QObject(parent)
    , m_checkTimer(new QTimer(this))
    , m_monitoring(false)
    , m_lastUSBKeyStatus(false)
#ifdef Q_OS_WIN
    , m_hwnd(nullptr)
#endif
{
    // 设置定时器，每2秒检查一次USB设备状态
    m_checkTimer->setInterval(2000);
    connect(m_checkTimer, &QTimer::timeout, this, &USBMonitor::checkUSBDevices);
}

USBMonitor::~USBMonitor()
{
    stopMonitoring();
}

void USBMonitor::startMonitoring()
{
    if (m_monitoring) {
        return;
    }
    
    qDebug() << "开始监听USB设备...";
    
    // 获取初始设备列表
    m_currentDevices = scanUSBDevices();
    m_lastUSBKeyStatus = hasUSBKeyConnected();
    
    // 启动定时器
    m_checkTimer->start();
    m_monitoring = true;
    
    // 发送初始状态
    emit usbStatusChanged(m_lastUSBKeyStatus);
}

void USBMonitor::stopMonitoring()
{
    if (!m_monitoring) {
        return;
    }
    
    qDebug() << "停止监听USB设备...";
    
    m_checkTimer->stop();
    m_monitoring = false;
}

QList<USBMonitor::USBDeviceInfo> USBMonitor::getCurrentUSBKeys()
{
    QList<USBDeviceInfo> usbKeys;
    
    foreach (const USBDeviceInfo& device, m_currentDevices) {
        if (device.isUSBKey) {
            usbKeys.append(device);
        }
    }
    
    return usbKeys;
}

bool USBMonitor::hasUSBKeyConnected()
{
    return !getCurrentUSBKeys().isEmpty();
}

void USBMonitor::checkUSBDevices()
{
    QList<USBDeviceInfo> newDevices = scanUSBDevices();
    
    // 比较设备列表变化
    compareDeviceLists(m_currentDevices, newDevices);
    
    // 更新当前设备列表
    m_currentDevices = newDevices;
    
    // 检查USB Key状态变化
    bool currentUSBKeyStatus = hasUSBKeyConnected();
    if (currentUSBKeyStatus != m_lastUSBKeyStatus) {
        m_lastUSBKeyStatus = currentUSBKeyStatus;
        emit usbStatusChanged(currentUSBKeyStatus);
    }
}

QList<USBMonitor::USBDeviceInfo> USBMonitor::scanUSBDevices()
{
    QList<USBDeviceInfo> devices;
    
#ifdef Q_OS_WIN
    // 获取所有逻辑驱动器
    DWORD drives = GetLogicalDrives();
    
    for (int i = 0; i < 26; i++) {
        if (drives & (1 << i)) {
            QString driveLetter = QString("%1:").arg(char('A' + i));
            
            // 检查驱动器类型
            UINT driveType = GetDriveTypeA(driveLetter.toLocal8Bit().data());
            
            if (driveType == DRIVE_REMOVABLE) {
                USBDeviceInfo deviceInfo;
                deviceInfo.driveLetter = driveLetter;
                deviceInfo.deviceName = getDeviceName(driveLetter);
                deviceInfo.isUSBKey = isUSBKey(driveLetter);
                deviceInfo.deviceId = QString("%1_%2").arg(driveLetter).arg(deviceInfo.deviceName);
                
                if (deviceInfo.isUSBKey) {
                    devices.append(deviceInfo);
                }
            }
        }
    }
#else
    // Linux/Mac实现可以在这里添加
    QList<QStorageInfo> storageList = QStorageInfo::mountedVolumes();
    foreach (const QStorageInfo& storage, storageList) {
        if (storage.isValid() && storage.isReady()) {
            // 简单判断是否为可移动设备
            QString devicePath = storage.device();
            if (devicePath.contains("usb", Qt::CaseInsensitive)) {
                USBDeviceInfo deviceInfo;
                deviceInfo.driveLetter = storage.rootPath();
                deviceInfo.deviceName = storage.displayName();
                deviceInfo.isUSBKey = true;
                deviceInfo.deviceId = devicePath;
                devices.append(deviceInfo);
            }
        }
    }
#endif
    
    return devices;
}

bool USBMonitor::isUSBKey(const QString& driveLetter)
{
#ifdef Q_OS_WIN
    QString devicePath = QString("\\\\.\\%1").arg(driveLetter);
    
    HANDLE hDevice = CreateFileA(devicePath.toLocal8Bit().data(),
                                0,
                                FILE_SHARE_READ | FILE_SHARE_WRITE,
                                nullptr,
                                OPEN_EXISTING,
                                0,
                                nullptr);
    
    if (hDevice == INVALID_HANDLE_VALUE) {
        return false;
    }
    
    STORAGE_PROPERTY_QUERY query;
    query.PropertyId = StorageDeviceProperty;
    query.QueryType = PropertyStandardQuery;
    
    STORAGE_DEVICE_DESCRIPTOR descriptor;
    DWORD bytesReturned;
    
    BOOL result = DeviceIoControl(hDevice,
                                 IOCTL_STORAGE_QUERY_PROPERTY,
                                 &query,
                                 sizeof(query),
                                 &descriptor,
                                 sizeof(descriptor),
                                 &bytesReturned,
                                 nullptr);
    
    CloseHandle(hDevice);
    
    if (result) {
        // 检查是否为可移动设备且通过USB连接
        return (descriptor.RemovableMedia && 
                descriptor.BusType == BusTypeUsb);
    }
#endif
    
    return true; // 默认认为是USB Key
}

QString USBMonitor::getDeviceName(const QString& driveLetter)
{
#ifdef Q_OS_WIN
    char volumeName[MAX_PATH + 1];
    char fileSystemName[MAX_PATH + 1];
    DWORD serialNumber;
    DWORD maxComponentLen;
    DWORD fileSystemFlags;
    
    QString driveRoot = driveLetter + "\\";
    
    if (GetVolumeInformationA(driveRoot.toLocal8Bit().data(),
                             volumeName,
                             sizeof(volumeName),
                             &serialNumber,
                             &maxComponentLen,
                             &fileSystemFlags,
                             fileSystemName,
                             sizeof(fileSystemName))) {
        QString name = QString::fromLocal8Bit(volumeName);
        if (name.isEmpty()) {
            name = QString("可移动磁盘 (%1)").arg(driveLetter);
        }
        return name;
    }
#endif
    
    return QString("USB设备 (%1)").arg(driveLetter);
}

void USBMonitor::compareDeviceLists(const QList<USBDeviceInfo>& oldList, 
                                   const QList<USBDeviceInfo>& newList)
{
    // 检查新插入的设备
    foreach (const USBDeviceInfo& newDevice, newList) {
        bool found = false;
        foreach (const USBDeviceInfo& oldDevice, oldList) {
            if (newDevice.deviceId == oldDevice.deviceId) {
                found = true;
                break;
            }
        }
        
        if (!found && newDevice.isUSBKey) {
            qDebug() << "USB Key插入:" << newDevice.getDisplayString();
            emit usbKeyInserted(newDevice);
        }
    }
    
    // 检查拔出的设备
    foreach (const USBDeviceInfo& oldDevice, oldList) {
        bool found = false;
        foreach (const USBDeviceInfo& newDevice, newList) {
            if (oldDevice.deviceId == newDevice.deviceId) {
                found = true;
                break;
            }
        }
        
        if (!found && oldDevice.isUSBKey) {
            qDebug() << "USB Key拔出:" << oldDevice.getDisplayString();
            emit usbKeyRemoved(oldDevice);
        }
    }
}
