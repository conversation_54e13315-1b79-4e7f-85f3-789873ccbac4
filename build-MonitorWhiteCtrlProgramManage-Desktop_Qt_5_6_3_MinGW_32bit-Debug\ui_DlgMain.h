/********************************************************************************
** Form generated from reading UI file 'DlgMain.ui'
**
** Created by: Qt User Interface Compiler version 5.6.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_DLGMAIN_H
#define UI_DLGMAIN_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QDialog>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QListWidget>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_CDlgMain
{
public:
    QVBoxLayout *verticalLayout_main;
    QLabel *label_title;
    QFrame *frame_status;
    QHBoxLayout *horizontalLayout_status;
    QLabel *label_usb_status;
    QLabel *label_usb_indicator;
    QSpacerItem *horizontalSpacer_status;
    QLabel *label_monitor_count;
    QLabel *label_whitelist_count;
    QLabel *label_detection_status;
    QFrame *frame_content;
    QHBoxLayout *horizontalLayout_content;
    QGroupBox *groupBox_current_monitors;
    QVBoxLayout *verticalLayout_current;
    QListWidget *listWidget_current_monitors;
    QPushButton *pushButton_add_to_whitelist;
    QGroupBox *groupBox_whitelist;
    QVBoxLayout *verticalLayout_whitelist;
    QListWidget *listWidget_whitelist;
    QHBoxLayout *horizontalLayout_whitelist_buttons;
    QPushButton *pushButton_remove_from_whitelist;
    QPushButton *pushButton_clear_whitelist;
    QTextEdit *textEdit_log;

    void setupUi(QDialog *CDlgMain)
    {
        if (CDlgMain->objectName().isEmpty())
            CDlgMain->setObjectName(QStringLiteral("CDlgMain"));
        CDlgMain->resize(900, 650);
        CDlgMain->setMinimumSize(QSize(900, 650));
        QIcon icon;
        icon.addFile(QStringLiteral("."), QSize(), QIcon::Normal, QIcon::Off);
        CDlgMain->setWindowIcon(icon);
        verticalLayout_main = new QVBoxLayout(CDlgMain);
        verticalLayout_main->setSpacing(15);
        verticalLayout_main->setContentsMargins(11, 11, 11, 11);
        verticalLayout_main->setObjectName(QStringLiteral("verticalLayout_main"));
        verticalLayout_main->setContentsMargins(20, 20, 20, 20);
        label_title = new QLabel(CDlgMain);
        label_title->setObjectName(QStringLiteral("label_title"));
        label_title->setAlignment(Qt::AlignCenter);

        verticalLayout_main->addWidget(label_title);

        frame_status = new QFrame(CDlgMain);
        frame_status->setObjectName(QStringLiteral("frame_status"));
        frame_status->setFrameShape(QFrame::StyledPanel);
        frame_status->setFrameShadow(QFrame::Raised);
        horizontalLayout_status = new QHBoxLayout(frame_status);
        horizontalLayout_status->setSpacing(20);
        horizontalLayout_status->setContentsMargins(11, 11, 11, 11);
        horizontalLayout_status->setObjectName(QStringLiteral("horizontalLayout_status"));
        label_usb_status = new QLabel(frame_status);
        label_usb_status->setObjectName(QStringLiteral("label_usb_status"));

        horizontalLayout_status->addWidget(label_usb_status);

        label_usb_indicator = new QLabel(frame_status);
        label_usb_indicator->setObjectName(QStringLiteral("label_usb_indicator"));

        horizontalLayout_status->addWidget(label_usb_indicator);

        horizontalSpacer_status = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_status->addItem(horizontalSpacer_status);

        label_monitor_count = new QLabel(frame_status);
        label_monitor_count->setObjectName(QStringLiteral("label_monitor_count"));

        horizontalLayout_status->addWidget(label_monitor_count);

        label_whitelist_count = new QLabel(frame_status);
        label_whitelist_count->setObjectName(QStringLiteral("label_whitelist_count"));

        horizontalLayout_status->addWidget(label_whitelist_count);

        label_detection_status = new QLabel(frame_status);
        label_detection_status->setObjectName(QStringLiteral("label_detection_status"));

        horizontalLayout_status->addWidget(label_detection_status);


        verticalLayout_main->addWidget(frame_status);

        frame_content = new QFrame(CDlgMain);
        frame_content->setObjectName(QStringLiteral("frame_content"));
        frame_content->setFrameShape(QFrame::NoFrame);
        horizontalLayout_content = new QHBoxLayout(frame_content);
        horizontalLayout_content->setSpacing(20);
        horizontalLayout_content->setContentsMargins(11, 11, 11, 11);
        horizontalLayout_content->setObjectName(QStringLiteral("horizontalLayout_content"));
        groupBox_current_monitors = new QGroupBox(frame_content);
        groupBox_current_monitors->setObjectName(QStringLiteral("groupBox_current_monitors"));
        verticalLayout_current = new QVBoxLayout(groupBox_current_monitors);
        verticalLayout_current->setSpacing(6);
        verticalLayout_current->setContentsMargins(11, 11, 11, 11);
        verticalLayout_current->setObjectName(QStringLiteral("verticalLayout_current"));
        listWidget_current_monitors = new QListWidget(groupBox_current_monitors);
        listWidget_current_monitors->setObjectName(QStringLiteral("listWidget_current_monitors"));

        verticalLayout_current->addWidget(listWidget_current_monitors);

        pushButton_add_to_whitelist = new QPushButton(groupBox_current_monitors);
        pushButton_add_to_whitelist->setObjectName(QStringLiteral("pushButton_add_to_whitelist"));

        verticalLayout_current->addWidget(pushButton_add_to_whitelist);


        horizontalLayout_content->addWidget(groupBox_current_monitors);

        groupBox_whitelist = new QGroupBox(frame_content);
        groupBox_whitelist->setObjectName(QStringLiteral("groupBox_whitelist"));
        verticalLayout_whitelist = new QVBoxLayout(groupBox_whitelist);
        verticalLayout_whitelist->setSpacing(6);
        verticalLayout_whitelist->setContentsMargins(11, 11, 11, 11);
        verticalLayout_whitelist->setObjectName(QStringLiteral("verticalLayout_whitelist"));
        listWidget_whitelist = new QListWidget(groupBox_whitelist);
        listWidget_whitelist->setObjectName(QStringLiteral("listWidget_whitelist"));

        verticalLayout_whitelist->addWidget(listWidget_whitelist);

        horizontalLayout_whitelist_buttons = new QHBoxLayout();
        horizontalLayout_whitelist_buttons->setSpacing(6);
        horizontalLayout_whitelist_buttons->setObjectName(QStringLiteral("horizontalLayout_whitelist_buttons"));
        pushButton_remove_from_whitelist = new QPushButton(groupBox_whitelist);
        pushButton_remove_from_whitelist->setObjectName(QStringLiteral("pushButton_remove_from_whitelist"));

        horizontalLayout_whitelist_buttons->addWidget(pushButton_remove_from_whitelist);

        pushButton_clear_whitelist = new QPushButton(groupBox_whitelist);
        pushButton_clear_whitelist->setObjectName(QStringLiteral("pushButton_clear_whitelist"));

        horizontalLayout_whitelist_buttons->addWidget(pushButton_clear_whitelist);


        verticalLayout_whitelist->addLayout(horizontalLayout_whitelist_buttons);


        horizontalLayout_content->addWidget(groupBox_whitelist);


        verticalLayout_main->addWidget(frame_content);

        textEdit_log = new QTextEdit(CDlgMain);
        textEdit_log->setObjectName(QStringLiteral("textEdit_log"));
        textEdit_log->setMaximumSize(QSize(16777215, 150));
        textEdit_log->setReadOnly(true);

        verticalLayout_main->addWidget(textEdit_log);


        retranslateUi(CDlgMain);

        QMetaObject::connectSlotsByName(CDlgMain);
    } // setupUi

    void retranslateUi(QDialog *CDlgMain)
    {
        CDlgMain->setWindowTitle(QApplication::translate("CDlgMain", "\346\230\276\347\244\272\345\231\250\347\231\275\345\220\215\345\215\225\346\216\247\345\210\266\347\250\213\345\272\217", Q_NULLPTR));
        label_title->setText(QApplication::translate("CDlgMain", "\346\230\276\347\244\272\345\231\250\347\231\275\345\220\215\345\215\225\347\256\241\347\220\206\347\263\273\347\273\237", Q_NULLPTR));
        label_title->setObjectName(QApplication::translate("CDlgMain", "titleLabel", Q_NULLPTR));
        frame_status->setObjectName(QApplication::translate("CDlgMain", "statusFrame", Q_NULLPTR));
        label_usb_status->setText(QApplication::translate("CDlgMain", "USB\347\212\266\346\200\201:", Q_NULLPTR));
        label_usb_indicator->setText(QApplication::translate("CDlgMain", "\346\234\252\350\277\236\346\216\245", Q_NULLPTR));
        label_usb_indicator->setObjectName(QApplication::translate("CDlgMain", "usbIndicator", Q_NULLPTR));
        label_monitor_count->setText(QApplication::translate("CDlgMain", "\350\277\236\346\216\245\346\230\276\347\244\272\345\231\250: 0", Q_NULLPTR));
        label_whitelist_count->setText(QApplication::translate("CDlgMain", "\347\231\275\345\220\215\345\215\225: 0", Q_NULLPTR));
        label_detection_status->setText(QApplication::translate("CDlgMain", "\346\243\200\346\265\213\347\272\277\347\250\213: \350\277\220\350\241\214\344\270\255", Q_NULLPTR));
        label_detection_status->setObjectName(QApplication::translate("CDlgMain", "detectionStatusLabel", Q_NULLPTR));
        groupBox_current_monitors->setTitle(QApplication::translate("CDlgMain", "\345\275\223\345\211\215\350\277\236\346\216\245\347\232\204\346\230\276\347\244\272\345\231\250", Q_NULLPTR));
        groupBox_current_monitors->setObjectName(QApplication::translate("CDlgMain", "currentMonitorsGroup", Q_NULLPTR));
        listWidget_current_monitors->setObjectName(QApplication::translate("CDlgMain", "currentMonitorsList", Q_NULLPTR));
        pushButton_add_to_whitelist->setText(QApplication::translate("CDlgMain", "\346\267\273\345\212\240\345\210\260\347\231\275\345\220\215\345\215\225", Q_NULLPTR));
        pushButton_add_to_whitelist->setObjectName(QApplication::translate("CDlgMain", "addToWhitelistBtn", Q_NULLPTR));
        groupBox_whitelist->setTitle(QApplication::translate("CDlgMain", "\346\230\276\347\244\272\345\231\250\347\231\275\345\220\215\345\215\225", Q_NULLPTR));
        groupBox_whitelist->setObjectName(QApplication::translate("CDlgMain", "whitelistGroup", Q_NULLPTR));
        listWidget_whitelist->setObjectName(QApplication::translate("CDlgMain", "whitelistList", Q_NULLPTR));
        pushButton_remove_from_whitelist->setText(QApplication::translate("CDlgMain", "\347\247\273\351\231\244\351\200\211\344\270\255", Q_NULLPTR));
        pushButton_remove_from_whitelist->setObjectName(QApplication::translate("CDlgMain", "removeFromWhitelistBtn", Q_NULLPTR));
        pushButton_clear_whitelist->setText(QApplication::translate("CDlgMain", "\346\270\205\347\251\272\347\231\275\345\220\215\345\215\225", Q_NULLPTR));
        pushButton_clear_whitelist->setObjectName(QApplication::translate("CDlgMain", "clearWhitelistBtn", Q_NULLPTR));
        textEdit_log->setObjectName(QApplication::translate("CDlgMain", "logTextEdit", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class CDlgMain: public Ui_CDlgMain {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_DLGMAIN_H
