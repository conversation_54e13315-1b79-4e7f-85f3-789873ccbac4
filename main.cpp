#include "DlgMain.h"
#include <QApplication>
#include <QDebug>
#include <QMessageBox>

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    qDebug() << "程序启动...";

    try {
        CDlgMain w;
        qDebug() << "主窗口创建成功";
        w.show();
        qDebug() << "主窗口显示成功";

        return a.exec();
    } catch (const std::exception& e) {
        qDebug() << "程序异常:" << e.what();
        QMessageBox::critical(nullptr, "错误", QString("程序启动失败: %1").arg(e.what()));
        return -1;
    } catch (...) {
        qDebug() << "未知异常";
        QMessageBox::critical(nullptr, "错误", "程序启动失败: 未知异常");
        return -1;
    }
}
