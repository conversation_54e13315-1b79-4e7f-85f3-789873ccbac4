<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CDlgMain</class>
 <widget class="QDialog" name="CDlgMain">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>900</width>
    <height>650</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>900</width>
    <height>650</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>显示器白名单控制程序</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>.</normaloff>.</iconset>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_main">
   <property name="spacing">
    <number>15</number>
   </property>
   <property name="leftMargin">
    <number>20</number>
   </property>
   <property name="topMargin">
    <number>20</number>
   </property>
   <property name="rightMargin">
    <number>20</number>
   </property>
   <property name="bottomMargin">
    <number>20</number>
   </property>
   <item>
    <widget class="QLabel" name="label_title">
     <property name="text">
      <string>显示器白名单管理系统</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
     <property name="objectName">
      <string>titleLabel</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="frame_status">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <property name="objectName">
      <string>statusFrame</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_status">
      <property name="spacing">
       <number>20</number>
      </property>
      <item>
       <widget class="QLabel" name="label_usb_status">
        <property name="text">
         <string>USB状态:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_usb_indicator">
        <property name="text">
         <string>未连接</string>
        </property>
        <property name="objectName">
         <string>usbIndicator</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_status">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="label_monitor_count">
        <property name="text">
         <string>连接显示器: 0</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_whitelist_count">
        <property name="text">
         <string>白名单: 0</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="frame_content">
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_content">
      <property name="spacing">
       <number>20</number>
      </property>
      <item>
       <widget class="QGroupBox" name="groupBox_current_monitors">
        <property name="title">
         <string>当前连接的显示器</string>
        </property>
        <property name="objectName">
         <string>currentMonitorsGroup</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_current">
         <item>
          <widget class="QListWidget" name="listWidget_current_monitors">
           <property name="objectName">
            <string>currentMonitorsList</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_add_to_whitelist">
           <property name="text">
            <string>添加到白名单</string>
           </property>
           <property name="objectName">
            <string>addToWhitelistBtn</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBox_whitelist">
        <property name="title">
         <string>显示器白名单</string>
        </property>
        <property name="objectName">
         <string>whitelistGroup</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_whitelist">
         <item>
          <widget class="QListWidget" name="listWidget_whitelist">
           <property name="objectName">
            <string>whitelistList</string>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_whitelist_buttons">
           <item>
            <widget class="QPushButton" name="pushButton_remove_from_whitelist">
             <property name="text">
              <string>移除选中</string>
             </property>
             <property name="objectName">
              <string>removeFromWhitelistBtn</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_clear_whitelist">
             <property name="text">
              <string>清空白名单</string>
             </property>
             <property name="objectName">
              <string>clearWhitelistBtn</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QTextEdit" name="textEdit_log">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>150</height>
      </size>
     </property>
     <property name="readOnly">
      <bool>true</bool>
     </property>
     <property name="objectName">
      <string>logTextEdit</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutDefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
