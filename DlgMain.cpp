#include "DlgMain.h"
#include "ui_DlgMain.h"
#include <QMessageBox>
#include <QCloseEvent>
#include <QDateTime>
#include <QDebug>

CDlgMain::CDlgMain(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::CDlgMain),
    m_usbMonitor(nullptr),
    m_monitorManager(nullptr),
    m_detectionThread(nullptr)
{
    ui->setupUi(this);

    // 初始化界面
    initializeUI();

    // 设置样式表
    setupStyleSheet();

    // 创建监听器
    m_usbMonitor = new USBMonitor(this);
    m_monitorManager = new MonitorManager(this);

    // 创建多线程检测器
    m_detectionThread = new MonitorDetectionThread(this);
    m_detectionThread->setDetectionInterval(1000); // 设置1秒间隔

    // 连接信号槽
    connectSignals();

    // 初始化管理器
    m_monitorManager->initialize();

    // 开始监听
    m_usbMonitor->startMonitoring();
    m_monitorManager->startMonitoring();

    // 启动多线程检测
    m_detectionThread->startDetection();

    // 更新界面显示
    updateCurrentMonitorsList();
    updateWhitelistDisplay();
    updateStatusDisplay();

    addLogMessage("程序启动完成，开始监听USB和显示器状态");
    addLogMessage("多线程显示器检测已启动，检测间隔: 1秒");
}

CDlgMain::~CDlgMain()
{
    if (m_detectionThread) {
        m_detectionThread->stopDetection();
    }

    if (m_usbMonitor) {
        m_usbMonitor->stopMonitoring();
    }

    if (m_monitorManager) {
        m_monitorManager->stopMonitoring();
    }

    delete ui;
}

void CDlgMain::closeEvent(QCloseEvent *event)
{
    addLogMessage("程序正在退出...");

    // 停止多线程检测
    if (m_detectionThread) {
        addLogMessage("停止多线程检测...");
        m_detectionThread->stopDetection();
    }

    // 停止监听
    if (m_usbMonitor) {
        m_usbMonitor->stopMonitoring();
    }

    if (m_monitorManager) {
        m_monitorManager->stopMonitoring();
    }

    addLogMessage("程序退出完成");
    event->accept();
}

void CDlgMain::initializeUI()
{
    // 设置窗口属性
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);

    // 设置列表控件属性
    ui->listWidget_current_monitors->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->listWidget_whitelist->setSelectionMode(QAbstractItemView::SingleSelection);

    // 设置日志文本框
    // Qt 5.6.3中QTextEdit没有setMaximumBlockCount方法，使用document()->setMaximumBlockCount
    ui->textEdit_log->document()->setMaximumBlockCount(1000); // 限制最大行数
}

void CDlgMain::setupStyleSheet()
{
    QString styleSheet = R"(
        /* 主窗口样式 */
        QDialog {
            background-color: #f5f5f5;
            font-family: "Microsoft YaHei", "SimHei", sans-serif;
        }

        /* 标题样式 */
        QLabel#titleLabel {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border: 2px solid #bdc3c7;
        }

        /* 状态框架样式 */
        QFrame#statusFrame {
            background-color: #ffffff;
            border: 1px solid #bdc3c7;
            border-radius: 6px;
            padding: 8px;
        }

        /* USB状态指示器样式 */
        QLabel#usbIndicator {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
        }

        /* 检测状态指示器样式 */
        QLabel#detectionStatusLabel {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            background-color: #3498db;
            color: white;
        }

        /* 分组框样式 */
        QGroupBox#currentMonitorsGroup, QGroupBox#whitelistGroup {
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            border: 2px solid #3498db;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
        }

        QGroupBox#currentMonitorsGroup::title, QGroupBox#whitelistGroup::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            background-color: #3498db;
            color: white;
            border-radius: 4px;
        }

        /* 列表控件样式 */
        QListWidget#currentMonitorsList, QListWidget#whitelistList {
            background-color: #ffffff;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            padding: 5px;
            font-size: 12px;
        }

        QListWidget#currentMonitorsList::item, QListWidget#whitelistList::item {
            padding: 8px;
            border-bottom: 1px solid #ecf0f1;
            border-radius: 3px;
            margin: 2px;
        }

        QListWidget#currentMonitorsList::item:selected, QListWidget#whitelistList::item:selected {
            background-color: #3498db;
            color: white;
        }

        QListWidget#currentMonitorsList::item:hover, QListWidget#whitelistList::item:hover {
            background-color: #ecf0f1;
        }

        /* 按钮样式 */
        QPushButton#addToWhitelistBtn {
            background-color: #27ae60;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px;
            font-size: 13px;
            font-weight: bold;
        }

        QPushButton#addToWhitelistBtn:hover {
            background-color: #2ecc71;
        }

        QPushButton#addToWhitelistBtn:pressed {
            background-color: #229954;
        }

        QPushButton#removeFromWhitelistBtn {
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px;
            font-size: 13px;
            font-weight: bold;
        }

        QPushButton#removeFromWhitelistBtn:hover {
            background-color: #c0392b;
        }

        QPushButton#clearWhitelistBtn {
            background-color: #f39c12;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px;
            font-size: 13px;
            font-weight: bold;
        }

        QPushButton#clearWhitelistBtn:hover {
            background-color: #e67e22;
        }

        /* 日志文本框样式 */
        QTextEdit#logTextEdit {
            background-color: #2c3e50;
            color: #ecf0f1;
            border: 1px solid #34495e;
            border-radius: 4px;
            font-family: "Consolas", "Courier New", monospace;
            font-size: 11px;
        }
    )";

    setStyleSheet(styleSheet);
}

void CDlgMain::connectSignals()
{
    // USB监听信号连接
    connect(m_usbMonitor, &USBMonitor::usbKeyInserted,
            this, &CDlgMain::onUSBKeyInserted);
    connect(m_usbMonitor, &USBMonitor::usbKeyRemoved,
            this, &CDlgMain::onUSBKeyRemoved);
    connect(m_usbMonitor, &USBMonitor::usbStatusChanged,
            this, &CDlgMain::onUSBStatusChanged);

    // 显示器监听信号连接
    connect(m_monitorManager, &MonitorManager::monitorConnected,
            this, &CDlgMain::onMonitorConnected);
    connect(m_monitorManager, &MonitorManager::monitorDisconnected,
            this, &CDlgMain::onMonitorDisconnected);
    connect(m_monitorManager, &MonitorManager::monitorListUpdated,
            this, &CDlgMain::onMonitorListUpdated);
    connect(m_monitorManager, &MonitorManager::whitelistUpdated,
            this, &CDlgMain::onWhitelistUpdated);

    // 多线程检测信号连接
    connect(m_detectionThread, &MonitorDetectionThread::monitorDetected,
            this, &CDlgMain::onMonitorDetected);
    connect(m_detectionThread, &MonitorDetectionThread::monitorLost,
            this, &CDlgMain::onMonitorLost);
    connect(m_detectionThread, &MonitorDetectionThread::monitorsUpdated,
            this, &CDlgMain::onMonitorsUpdated);
    connect(m_detectionThread, &MonitorDetectionThread::detectionStatusUpdated,
            this, &CDlgMain::onDetectionStatusUpdated);
    connect(m_detectionThread, &MonitorDetectionThread::errorOccurred,
            this, &CDlgMain::onDetectionErrorOccurred);

    // 界面操作信号连接
    connect(ui->pushButton_add_to_whitelist, &QPushButton::clicked,
            this, &CDlgMain::onAddToWhitelistClicked);
    connect(ui->pushButton_remove_from_whitelist, &QPushButton::clicked,
            this, &CDlgMain::onRemoveFromWhitelistClicked);
    connect(ui->pushButton_clear_whitelist, &QPushButton::clicked,
            this, &CDlgMain::onClearWhitelistClicked);

    // 列表双击信号连接
    connect(ui->listWidget_current_monitors, &QListWidget::itemDoubleClicked,
            this, &CDlgMain::onCurrentMonitorItemDoubleClicked);
    connect(ui->listWidget_whitelist, &QListWidget::itemDoubleClicked,
            this, &CDlgMain::onWhitelistItemDoubleClicked);
}

// USB监听相关槽函数
void CDlgMain::onUSBKeyInserted(const USBMonitor::USBDeviceInfo& deviceInfo)
{
    addLogMessage(QString("USB Key插入: %1").arg(deviceInfo.getDisplayString()));
    updateStatusDisplay();
}

void CDlgMain::onUSBKeyRemoved(const USBMonitor::USBDeviceInfo& deviceInfo)
{
    addLogMessage(QString("USB Key拔出: %1").arg(deviceInfo.getDisplayString()));
    updateStatusDisplay();
}

void CDlgMain::onUSBStatusChanged(bool hasUSBKey)
{
    // 更新USB状态指示器
    if (hasUSBKey) {
        ui->label_usb_indicator->setText("已连接");
        ui->label_usb_indicator->setStyleSheet("background-color: #27ae60; color: white;");
    } else {
        ui->label_usb_indicator->setText("未连接");
        ui->label_usb_indicator->setStyleSheet("background-color: #e74c3c; color: white;");
    }
}

// 显示器监听相关槽函数
void CDlgMain::onMonitorConnected(const EDIDParser::EDIDInfo& monitorInfo)
{
    addLogMessage(QString("显示器连接: %1").arg(monitorInfo.getDisplayString()));
    updateCurrentMonitorsList();
    updateStatusDisplay();
}

void CDlgMain::onMonitorDisconnected(const EDIDParser::EDIDInfo& monitorInfo)
{
    addLogMessage(QString("显示器断开: %1").arg(monitorInfo.getDisplayString()));
    updateCurrentMonitorsList();
    updateStatusDisplay();
}

void CDlgMain::onMonitorListUpdated()
{
    updateCurrentMonitorsList();
    updateStatusDisplay();
}

void CDlgMain::onWhitelistUpdated()
{
    updateWhitelistDisplay();
    updateStatusDisplay();
}

// 界面操作槽函数
void CDlgMain::onAddToWhitelistClicked()
{
    QListWidgetItem* currentItem = ui->listWidget_current_monitors->currentItem();
    if (!currentItem) {
        QMessageBox::information(this, "提示", "请先选择要添加的显示器");
        return;
    }

    QString uniqueId = getMonitorUniqueId(currentItem);
    if (uniqueId.isEmpty()) {
        QMessageBox::warning(this, "错误", "无法获取显示器唯一标识");
        return;
    }

    if (m_monitorManager->isInWhitelist(uniqueId)) {
        QMessageBox::information(this, "提示", "该显示器已在白名单中");
        return;
    }

    if (m_monitorManager->addToWhitelist(uniqueId)) {
        EDIDParser::EDIDInfo monitorInfo = m_monitorManager->getMonitorInfo(uniqueId);
        addLogMessage(QString("添加到白名单: %1").arg(monitorInfo.getDisplayString()));
    } else {
        QMessageBox::warning(this, "错误", "添加到白名单失败");
    }
}

void CDlgMain::onRemoveFromWhitelistClicked()
{
    QListWidgetItem* currentItem = ui->listWidget_whitelist->currentItem();
    if (!currentItem) {
        QMessageBox::information(this, "提示", "请先选择要移除的显示器");
        return;
    }

    QString uniqueId = getMonitorUniqueId(currentItem);
    if (uniqueId.isEmpty()) {
        QMessageBox::warning(this, "错误", "无法获取显示器唯一标识");
        return;
    }

    if (m_monitorManager->removeFromWhitelist(uniqueId)) {
        addLogMessage(QString("从白名单移除: %1").arg(currentItem->text()));
    } else {
        QMessageBox::warning(this, "错误", "从白名单移除失败");
    }
}

void CDlgMain::onClearWhitelistClicked()
{
    if (m_monitorManager->getWhitelistMonitors().isEmpty()) {
        QMessageBox::information(this, "提示", "白名单已经为空");
        return;
    }

    int ret = QMessageBox::question(this, "确认", "确定要清空白名单吗？",
                                   QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes) {
        m_monitorManager->clearWhitelist();
        addLogMessage("白名单已清空");
    }
}

void CDlgMain::onCurrentMonitorItemDoubleClicked(QListWidgetItem* item)
{
    if (item) {
        onAddToWhitelistClicked();
    }
}

void CDlgMain::onWhitelistItemDoubleClicked(QListWidgetItem* item)
{
    if (item) {
        QString uniqueId = getMonitorUniqueId(item);
        if (!uniqueId.isEmpty()) {
            QString info = QString("显示器唯一标识: %1\n显示名称: %2")
                          .arg(uniqueId)
                          .arg(item->text());
            QMessageBox::information(this, "显示器信息", info);
        }
    }
}

// 多线程检测相关槽函数
void CDlgMain::onMonitorDetected(const EDIDParser::EDIDInfo& monitorInfo)
{
    addLogMessage(QString("线程检测到新显示器: %1").arg(monitorInfo.getDisplayString()));

    // 更新界面显示
    updateCurrentMonitorsList();
    updateStatusDisplay();
}

void CDlgMain::onMonitorLost(const EDIDParser::EDIDInfo& monitorInfo)
{
    addLogMessage(QString("线程检测到显示器断开: %1").arg(monitorInfo.getDisplayString()));

    // 更新界面显示
    updateCurrentMonitorsList();
    updateStatusDisplay();
}

void CDlgMain::onMonitorsUpdated(const QList<EDIDParser::EDIDInfo>& monitors)
{
    // 使用线程检测的结果更新界面
    ui->listWidget_current_monitors->clear();

    foreach (const EDIDParser::EDIDInfo& monitor, monitors) {
        QListWidgetItem* item = new QListWidgetItem();
        item->setText(monitor.getDisplayString());
        item->setData(Qt::UserRole, monitor.uniqueId); // 存储唯一标识

        // 如果已在白名单中，显示不同的样式
        if (m_monitorManager && m_monitorManager->isInWhitelist(monitor.uniqueId)) {
            item->setToolTip("该显示器已在白名单中");
            // 设置绿色背景表示已在白名单
            item->setBackground(QBrush(QColor("#d5f4e6")));
        } else {
            item->setToolTip(QString("唯一标识: %1").arg(monitor.uniqueId));
        }

        ui->listWidget_current_monitors->addItem(item);
    }

    // 更新状态显示
    updateStatusDisplay();
}

void CDlgMain::onDetectionStatusUpdated(bool isRunning, int monitorCount)
{
    static int lastMonitorCount = -1;

    // 只在显示器数量变化时输出日志，避免过多日志
    if (monitorCount != lastMonitorCount) {
        if (isRunning) {
            addLogMessage(QString("检测线程运行中，当前检测到 %1 个显示器").arg(monitorCount));
        } else {
            addLogMessage("检测线程已停止");
        }
        lastMonitorCount = monitorCount;
    }

    // 更新状态显示
    ui->label_monitor_count->setText(QString("连接显示器: %1").arg(monitorCount));

    // 更新检测状态标签
    if (isRunning) {
        ui->label_detection_status->setText("检测线程: 运行中");
        ui->label_detection_status->setStyleSheet("background-color: #27ae60; color: white;");
    } else {
        ui->label_detection_status->setText("检测线程: 已停止");
        ui->label_detection_status->setStyleSheet("background-color: #e74c3c; color: white;");
    }
}

void CDlgMain::onDetectionErrorOccurred(const QString& errorMessage)
{
    addLogMessage(QString("检测线程错误: %1").arg(errorMessage));

    // 可以选择显示错误对话框
    // QMessageBox::warning(this, "检测错误", errorMessage);
}

// 辅助函数实现
void CDlgMain::updateCurrentMonitorsList()
{
    // 如果有检测线程，使用线程的数据
    if (m_detectionThread && m_detectionThread->isDetectionRunning()) {
        QList<EDIDParser::EDIDInfo> monitors = m_detectionThread->getCurrentMonitors();

        ui->listWidget_current_monitors->clear();
        foreach (const EDIDParser::EDIDInfo& monitor, monitors) {
            QListWidgetItem* item = new QListWidgetItem();
            item->setText(monitor.getDisplayString());
            item->setData(Qt::UserRole, monitor.uniqueId); // 存储唯一标识

            // 如果已在白名单中，显示不同的样式
            if (m_monitorManager && m_monitorManager->isInWhitelist(monitor.uniqueId)) {
                item->setToolTip("该显示器已在白名单中");
                item->setBackground(QBrush(QColor("#d5f4e6")));
            } else {
                item->setToolTip(QString("唯一标识: %1").arg(monitor.uniqueId));
            }

            ui->listWidget_current_monitors->addItem(item);
        }
    } else {
        // 回退到使用管理器的数据
        ui->listWidget_current_monitors->clear();

        if (m_monitorManager) {
            QList<EDIDParser::EDIDInfo> monitors = m_monitorManager->getCurrentMonitors();
            foreach (const EDIDParser::EDIDInfo& monitor, monitors) {
                QListWidgetItem* item = new QListWidgetItem();
                item->setText(monitor.getDisplayString());
                item->setData(Qt::UserRole, monitor.uniqueId); // 存储唯一标识

                // 如果已在白名单中，显示不同的样式
                if (m_monitorManager->isInWhitelist(monitor.uniqueId)) {
                    item->setToolTip("该显示器已在白名单中");
                    item->setBackground(QBrush(QColor("#d5f4e6")));
                }

                ui->listWidget_current_monitors->addItem(item);
            }
        }
    }
}

void CDlgMain::updateWhitelistDisplay()
{
    ui->listWidget_whitelist->clear();

    QStringList whitelistIds = m_monitorManager->getWhitelistMonitors();
    foreach (const QString& uniqueId, whitelistIds) {
        QListWidgetItem* item = new QListWidgetItem();
        item->setData(Qt::UserRole, uniqueId); // 存储唯一标识

        // 尝试获取显示器信息
        EDIDParser::EDIDInfo monitorInfo = m_monitorManager->getMonitorInfo(uniqueId);
        if (monitorInfo.isValid()) {
            item->setText(monitorInfo.getDisplayString());
            item->setToolTip("当前已连接");
        } else {
            item->setText(QString("未连接的显示器 (%1)").arg(uniqueId));
            item->setToolTip("显示器未连接");
            // 设置不同的文本颜色表示未连接
            item->setForeground(QBrush(QColor("#7f8c8d")));
        }

        ui->listWidget_whitelist->addItem(item);
    }
}

void CDlgMain::updateStatusDisplay()
{
    // 更新显示器数量
    int monitorCount = m_monitorManager->getCurrentMonitors().size();
    ui->label_monitor_count->setText(QString("连接显示器: %1").arg(monitorCount));

    // 更新白名单数量
    int whitelistCount = m_monitorManager->getWhitelistMonitors().size();
    ui->label_whitelist_count->setText(QString("白名单: %1").arg(whitelistCount));
}

void CDlgMain::addLogMessage(const QString& message)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
    QString logMessage = QString("[%1] %2").arg(timestamp).arg(message);

    ui->textEdit_log->append(logMessage);

    // 自动滚动到底部
    QTextCursor cursor = ui->textEdit_log->textCursor();
    cursor.movePosition(QTextCursor::End);
    ui->textEdit_log->setTextCursor(cursor);

    // 输出到调试控制台
    qDebug() << logMessage;
}

QString CDlgMain::getMonitorUniqueId(QListWidgetItem* item)
{
    if (!item) {
        return QString();
    }

    return item->data(Qt::UserRole).toString();
}
