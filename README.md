# 显示器白名单控制程序

## 项目概述

这是一个基于Qt 5.6.3开发的显示器白名单管理程序，具有以下主要功能：

### 核心功能

1. **USB Key监听**
   - 实时监听USB Key的插入和拔出事件
   - 显示当前连接的USB Key状态
   - 支持多个USB Key同时监听

2. **显示器EDID管理**
   - 自动检测当前连接的显示器
   - 解析显示器EDID数据生成唯一标识
   - 支持显示器热插拔检测

3. **白名单管理**
   - 添加显示器到白名单
   - 从白名单移除显示器
   - 清空整个白名单
   - 白名单数据持久化存储

### 界面特色

- **优美的现代化界面**：使用QSS样式表设计
- **实时状态显示**：USB状态指示器、显示器数量统计
- **双列表设计**：当前连接显示器 vs 白名单显示器
- **操作日志**：实时显示程序运行日志
- **简单易用**：支持双击添加、右键操作等

## 技术架构

### 核心类设计

1. **EDIDParser类**
   - 解析显示器EDID数据
   - 生成基于制造商、产品代码、序列号的唯一标识
   - 从Windows注册表读取EDID信息

2. **USBMonitor类**
   - 监听USB设备插拔事件
   - 识别USB Key设备
   - 定时扫描USB设备状态

3. **MonitorManager类**
   - 管理显示器白名单
   - 监听显示器连接状态
   - 数据持久化存储

4. **CDlgMain类**
   - 主界面控制器
   - 信号槽连接管理
   - 用户交互处理

### 技术特点

- **Qt 5.6.3兼容**：遵循Qt 5.6.3 API规范
- **Windows平台优化**：使用Windows API获取硬件信息
- **线程安全**：使用Qt信号槽机制确保线程安全
- **内存管理**：使用智能指针和RAII原则
- **异常处理**：完善的异常捕获和处理机制

## 编译说明

### 环境要求

- Qt 5.6.3 (MinGW 4.9.2)
- Windows 10/11
- MinGW编译器

### 编译步骤

1. 打开Qt Creator或使用命令行
2. 加载项目文件 `MonitorWhiteCtrlProgramManage.pro`
3. 配置编译环境
4. 编译项目

```bash
# 命令行编译
qmake MonitorWhiteCtrlProgramManage.pro
mingw32-make debug
```

### 运行程序

```bash
# 设置环境变量
set PATH=D:\Qt\Qt5.6.3\5.6.3\mingw49_32\bin;%PATH%

# 运行程序
debug\MonitorWhiteCtrlProgramManage.exe
```

## 文件结构

```
├── main.cpp                    # 程序入口
├── DlgMain.h/cpp/ui           # 主界面
├── EDIDParser.h/cpp           # EDID解析器
├── USBMonitor.h/cpp           # USB监听器
├── MonitorManager.h/cpp       # 显示器管理器
├── MonitorWhiteCtrlProgramManage.pro  # 项目文件
└── README.md                  # 说明文档
```

## 使用说明

1. **启动程序**：运行可执行文件
2. **查看状态**：顶部状态栏显示USB和显示器状态
3. **添加白名单**：选择左侧显示器，点击"添加到白名单"
4. **移除白名单**：选择右侧白名单项，点击"移除选中"
5. **清空白名单**：点击"清空白名单"按钮
6. **查看日志**：底部日志区域显示操作记录

## 注意事项

- 程序需要管理员权限以访问硬件信息
- 白名单数据保存在用户AppData目录
- 支持显示器热插拔，但USB监听需要轮询检测
- 界面使用中文，确保系统支持UTF-8编码

## 开发者信息

- 开发环境：Qt 5.6.3 + MinGW
- 编码规范：遵循Qt编码规范
- 内存管理：使用智能指针，避免内存泄漏
- 异常安全：完善的异常处理机制
