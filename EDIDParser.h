#ifndef EDIDPARSER_H
#define EDIDPARSER_H

#include <QString>
#include <QByteArray>
#include <QStringList>
#include <QList>

#ifdef Q_OS_WIN
#include <windows.h>
#include <setupapi.h>
#include <cfgmgr32.h>
#include <winioctl.h>
#include <ntddvdeo.h>

// 声明显示器设备类GUID（在cpp文件中定义）
extern const GUID GUID_DEVCLASS_MONITOR;
#endif

/**
 * @brief EDID数据解析器类
 * 用于解析显示器EDID数据并生成唯一标识
 */
class EDIDParser
{
public:
    /**
     * @brief EDID数据结构
     */
    struct EDIDInfo
    {
        QString manufacturer;       // 制造商
        QString productCode;        // 产品代码
        QString serialNumber;       // 序列号
        QString modelName;          // 型号名称
        QString uniqueId;           // 唯一标识
        int width;                  // 屏幕宽度(mm)
        int height;                 // 屏幕高度(mm)
        QByteArray rawData;         // 原始EDID数据
        
        EDIDInfo() : width(0), height(0) {}
        
        bool isValid() const {
            return !manufacturer.isEmpty() && !uniqueId.isEmpty();
        }

        QString getDisplayString() const {
            return QString("%1 %2 (%3)").arg(manufacturer).arg(modelName).arg(uniqueId);
        }
    };

public:
    EDIDParser();
    ~EDIDParser();

    /**
     * @brief 解析EDID数据
     * @param edidData 原始EDID数据
     * @return 解析后的EDID信息
     */
    static EDIDInfo parseEDID(const QByteArray& edidData);

    /**
     * @brief 生成显示器唯一标识
     * @param edidInfo EDID信息
     * @return 唯一标识字符串
     */
    static QString generateUniqueId(const EDIDInfo& edidInfo);

    /**
     * @brief 获取所有连接的显示器EDID信息
     * @return 显示器EDID信息列表
     */
    static QList<EDIDInfo> getAllConnectedMonitors();

    /**
     * @brief 验证EDID数据有效性（支持扩展EDID）
     * @param edidData EDID数据（128、256、384、512字节等）
     * @return 是否有效
     */
    static bool isValidEDID(const QByteArray& edidData);

    /**
     * @brief 获取EDID数据长度信息
     * @param edidData EDID数据
     * @return 长度信息字符串
     */
    static QString getEDIDLengthInfo(const QByteArray& edidData);

private:
    /**
     * @brief 解析制造商ID
     * @param data EDID数据
     * @return 制造商字符串
     */
    static QString parseManufacturer(const QByteArray& data);

    /**
     * @brief 解析产品代码
     * @param data EDID数据
     * @return 产品代码
     */
    static QString parseProductCode(const QByteArray& data);

    /**
     * @brief 解析序列号
     * @param data EDID数据
     * @return 序列号
     */
    static QString parseSerialNumber(const QByteArray& data);

    /**
     * @brief 解析型号名称
     * @param data EDID数据
     * @return 型号名称
     */
    static QString parseModelName(const QByteArray& data);

    /**
     * @brief 解析屏幕尺寸
     * @param data EDID数据
     * @param width 输出宽度
     * @param height 输出高度
     */
    static void parseScreenSize(const QByteArray& data, int& width, int& height);

    /**
     * @brief 计算EDID校验和
     * @param data EDID数据
     * @return 校验和是否正确
     */
    static bool validateChecksum(const QByteArray& data);

#ifdef Q_OS_WIN
    /**
     * @brief 使用Windows Display API直接获取EDID
     * @return EDID数据列表
     */
    static QList<QByteArray> getEDIDFromDisplayAPI();

    /**
     * @brief 使用SetupAPI枚举显示器设备
     * @return EDID数据列表
     */
    static QList<QByteArray> getEDIDFromSetupAPI();

    /**
     * @brief 使用WMI获取显示器EDID数据
     * @return EDID数据列表
     */
    static QList<QByteArray> getEDIDFromWMI();

    /**
     * @brief 直接从显示器设备获取EDID数据
     * @param devicePath 设备路径
     * @return EDID数据
     */
    static QByteArray getEDIDFromDevice(const QString& devicePath);

    /**
     * @brief 通过DeviceIoControl获取EDID数据
     * @param hDevice 设备句柄
     * @return EDID数据
     */
    static QByteArray getEDIDFromDeviceHandle(HANDLE hDevice);

    /**
     * @brief 枚举所有显示器设备路径
     * @return 设备路径列表
     */
    static QStringList enumerateDisplayDevicePaths();

    /**
     * @brief 获取显示器物理设备路径
     * @param displayName 显示器名称
     * @return 物理设备路径
     */
    static QString getPhysicalDevicePath(const QString& displayName);
#endif
};

#endif // EDIDPARSER_H
