#include "MonitorManager.h"
#include <QDebug>
#include <QDir>
#include <QStandardPaths>
#include <QTextStream>
#include <QFile>
#include <QApplication>

MonitorManager::MonitorManager(QObject *parent)
    : QObject(parent)
    , m_checkTimer(new QTimer(this))
    , m_monitoring(false)
{
    // 设置定时器，每3秒检查一次显示器状态
    m_checkTimer->setInterval(3000);
    connect(m_checkTimer, &QTimer::timeout, this, &MonitorManager::checkMonitorStatus);
    
    // 设置白名单文件路径
    QString configDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    m_whitelistFilePath = QDir(configDir).absoluteFilePath("monitor_whitelist.txt");
}

MonitorManager::~MonitorManager()
{
    stopMonitoring();
    saveWhitelist();
}

void MonitorManager::initialize()
{
    qDebug() << "初始化显示器管理器...";
    
    // 创建配置目录
    createConfigDirectory();
    
    // 加载白名单
    loadWhitelist();
    
    // 获取当前显示器列表
    m_currentMonitors = EDIDParser::getAllConnectedMonitors();
    
    qDebug() << QString("发现 %1 个显示器").arg(m_currentMonitors.size());
    foreach (const EDIDParser::EDIDInfo& monitor, m_currentMonitors) {
        qDebug() << "显示器:" << monitor.getDisplayString();
    }
}

void MonitorManager::startMonitoring()
{
    if (m_monitoring) {
        return;
    }
    
    qDebug() << "开始监听显示器状态...";
    
    // 启动定时器
    m_checkTimer->start();
    m_monitoring = true;
}

void MonitorManager::stopMonitoring()
{
    if (!m_monitoring) {
        return;
    }
    
    qDebug() << "停止监听显示器状态...";
    
    m_checkTimer->stop();
    m_monitoring = false;
}

QList<EDIDParser::EDIDInfo> MonitorManager::getCurrentMonitors()
{
    return m_currentMonitors;
}

QStringList MonitorManager::getWhitelistMonitors()
{
    return m_whitelist;
}

bool MonitorManager::addToWhitelist(const QString& uniqueId)
{
    if (uniqueId.isEmpty() || m_whitelist.contains(uniqueId)) {
        return false;
    }
    
    m_whitelist.append(uniqueId);
    saveWhitelist();
    
    qDebug() << "添加到白名单:" << uniqueId;
    emit whitelistUpdated();
    
    return true;
}

bool MonitorManager::removeFromWhitelist(const QString& uniqueId)
{
    if (!m_whitelist.contains(uniqueId)) {
        return false;
    }
    
    m_whitelist.removeAll(uniqueId);
    saveWhitelist();
    
    qDebug() << "从白名单移除:" << uniqueId;
    emit whitelistUpdated();
    
    return true;
}

void MonitorManager::clearWhitelist()
{
    if (m_whitelist.isEmpty()) {
        return;
    }
    
    m_whitelist.clear();
    saveWhitelist();
    
    qDebug() << "清空白名单";
    emit whitelistUpdated();
}

bool MonitorManager::isInWhitelist(const QString& uniqueId)
{
    return m_whitelist.contains(uniqueId);
}

QString MonitorManager::getWhitelistFilePath() const
{
    return m_whitelistFilePath;
}

EDIDParser::EDIDInfo MonitorManager::getMonitorInfo(const QString& uniqueId)
{
    foreach (const EDIDParser::EDIDInfo& monitor, m_currentMonitors) {
        if (monitor.uniqueId == uniqueId) {
            return monitor;
        }
    }
    
    return EDIDParser::EDIDInfo(); // 返回无效信息
}

void MonitorManager::checkMonitorStatus()
{
    QList<EDIDParser::EDIDInfo> newMonitors = EDIDParser::getAllConnectedMonitors();
    
    // 比较显示器列表变化
    compareMonitorLists(m_currentMonitors, newMonitors);
    
    // 更新当前显示器列表
    m_currentMonitors = newMonitors;
}

void MonitorManager::loadWhitelist()
{
    QFile file(m_whitelistFilePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "无法打开白名单文件，将创建新文件:" << m_whitelistFilePath;
        return;
    }
    
    QTextStream in(&file);
    in.setCodec("UTF-8");
    
    m_whitelist.clear();
    while (!in.atEnd()) {
        QString line = in.readLine().trimmed();
        if (!line.isEmpty() && !m_whitelist.contains(line)) {
            m_whitelist.append(line);
        }
    }
    
    file.close();
    
    qDebug() << QString("加载白名单，共 %1 个显示器").arg(m_whitelist.size());
}

void MonitorManager::saveWhitelist()
{
    QFile file(m_whitelistFilePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qDebug() << "无法保存白名单文件:" << m_whitelistFilePath;
        return;
    }
    
    QTextStream out(&file);
    out.setCodec("UTF-8");
    
    foreach (const QString& uniqueId, m_whitelist) {
        out << uniqueId << "\n";
    }
    
    file.close();
    
    qDebug() << "白名单已保存";
}

void MonitorManager::compareMonitorLists(const QList<EDIDParser::EDIDInfo>& oldList,
                                        const QList<EDIDParser::EDIDInfo>& newList)
{
    // 检查新连接的显示器
    foreach (const EDIDParser::EDIDInfo& newMonitor, newList) {
        bool found = false;
        foreach (const EDIDParser::EDIDInfo& oldMonitor, oldList) {
            if (newMonitor.uniqueId == oldMonitor.uniqueId) {
                found = true;
                break;
            }
        }
        
        if (!found) {
            qDebug() << "显示器连接:" << newMonitor.getDisplayString();
            emit monitorConnected(newMonitor);
        }
    }
    
    // 检查断开的显示器
    foreach (const EDIDParser::EDIDInfo& oldMonitor, oldList) {
        bool found = false;
        foreach (const EDIDParser::EDIDInfo& newMonitor, newList) {
            if (oldMonitor.uniqueId == newMonitor.uniqueId) {
                found = true;
                break;
            }
        }
        
        if (!found) {
            qDebug() << "显示器断开:" << oldMonitor.getDisplayString();
            emit monitorDisconnected(oldMonitor);
        }
    }
    
    // 如果列表有变化，发送更新信号
    if (oldList.size() != newList.size()) {
        emit monitorListUpdated();
    }
}

void MonitorManager::createConfigDirectory()
{
    QString configDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir dir;
    if (!dir.exists(configDir)) {
        if (dir.mkpath(configDir)) {
            qDebug() << "创建配置目录:" << configDir;
        } else {
            qDebug() << "无法创建配置目录:" << configDir;
        }
    }
}
