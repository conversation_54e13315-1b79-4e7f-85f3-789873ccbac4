#ifndef DLGMAIN_H
#define DLGMAIN_H

#include <QDialog>
#include <QListWidgetItem>
#include <QTimer>
#include "USBMonitor.h"
#include "MonitorManager.h"
#include "EDIDParser.h"

namespace Ui {
class CDlgMain;
}

class CDlgMain : public QDialog
{
    Q_OBJECT

public:
    explicit CDlgMain(QWidget *parent = 0);
    ~CDlgMain();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    // USB监听相关槽函数
    void onUSBKeyInserted(const USBMonitor::USBDeviceInfo& deviceInfo);
    void onUSBKeyRemoved(const USBMonitor::USBDeviceInfo& deviceInfo);
    void onUSBStatusChanged(bool hasUSBKey);

    // 显示器监听相关槽函数
    void onMonitorConnected(const EDIDParser::EDIDInfo& monitorInfo);
    void onMonitorDisconnected(const EDIDParser::EDIDInfo& monitorInfo);
    void onMonitorListUpdated();
    void onWhitelistUpdated();

    // 界面操作槽函数
    void onAddToWhitelistClicked();
    void onRemoveFromWhitelistClicked();
    void onClearWhitelistClicked();
    void onCurrentMonitorItemDoubleClicked(QListWidgetItem* item);
    void onWhitelistItemDoubleClicked(QListWidgetItem* item);

private:
    void initializeUI();
    void setupStyleSheet();
    void connectSignals();
    void updateCurrentMonitorsList();
    void updateWhitelistDisplay();
    void updateStatusDisplay();
    void addLogMessage(const QString& message);

    // 获取列表项对应的唯一标识
    QString getMonitorUniqueId(QListWidgetItem* item);

private:
    Ui::CDlgMain *ui;
    USBMonitor* m_usbMonitor;
    MonitorManager* m_monitorManager;
};

#endif // DLGMAIN_H
