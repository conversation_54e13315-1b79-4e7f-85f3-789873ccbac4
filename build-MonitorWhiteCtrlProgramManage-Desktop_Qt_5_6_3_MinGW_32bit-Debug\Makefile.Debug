#############################################################################
# Makefile for building: MonitorWhiteCtrlProgramManage
# Generated by qmake (3.0) (Qt 5.6.3)
# Project:  ..\MonitorWhiteCtrlProgramManage.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Debug

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -DQT_QML_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -pipe -fno-keep-inline-dllexport -g -Wall -Wextra $(DEFINES)
CXXFLAGS      = -pipe -fno-keep-inline-dllexport -g -std=gnu++0x -frtti -Wall -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I..\..\002_MonitorWhiteCtrlProgramManage -I. -ID:\Qt\Qt5.6.3\5.6.3\mingw49_32\include -ID:\Qt\Qt5.6.3\5.6.3\mingw49_32\include\QtWidgets -ID:\Qt\Qt5.6.3\5.6.3\mingw49_32\include\QtGui -ID:\Qt\Qt5.6.3\5.6.3\mingw49_32\include\QtANGLE -ID:\Qt\Qt5.6.3\5.6.3\mingw49_32\include\QtCore -Idebug -I. -ID:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-subsystem,windows -mthreads
LIBS        =        -lmingw32 -LD:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib D:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib\libqtmaind.a -lshell32 -LC:\utils\my_sql\my_sql\lib -LC:\utils\postgresql\pgsql\lib D:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib\libQt5Widgetsd.a D:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib\libQt5Guid.a D:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib\libQt5Cored.a 
QMAKE         = D:\Qt\Qt5.6.3\5.6.3\mingw49_32\bin\qmake.exe
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
COPY          = copy /y
SED           = $(QMAKE) -install sed
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
DEL_FILE      = del
DEL_DIR       = rmdir
MOVE          = move
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
INSTALL_FILE    = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR     = xcopy /s /q /y /i

####### Output directory

OBJECTS_DIR   = debug

####### Files

SOURCES       = ..\main.cpp \
		..\DlgMain.cpp debug\moc_DlgMain.cpp
OBJECTS       = debug/main.o \
		debug/DlgMain.o \
		debug/moc_DlgMain.o

DIST          =  ..\DlgMain.h ..\main.cpp \
		..\DlgMain.cpp
QMAKE_TARGET  = MonitorWhiteCtrlProgramManage
DESTDIR        = debug\ #avoid trailing-slash linebreak
TARGET         = MonitorWhiteCtrlProgramManage.exe
DESTDIR_TARGET = debug\MonitorWhiteCtrlProgramManage.exe

####### Build rules

first: all
all: Makefile.Debug  $(DESTDIR_TARGET)

$(DESTDIR_TARGET): ui_DlgMain.h $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) $(OBJECTS)  $(LIBS)

qmake: FORCE
	@$(QMAKE) -spec win32-g++ "CONFIG+=debug" "CONFIG+=qml_debug" -o Makefile.Debug ..\MonitorWhiteCtrlProgramManage.pro

qmake_all: FORCE

dist:
	$(ZIP) MonitorWhiteCtrlProgramManage.zip $(SOURCES) $(DIST) ..\MonitorWhiteCtrlProgramManage.pro D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\spec_pre.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\qdevice.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\device_config.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\common\angle.conf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\qconfig.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dcore.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dcore_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dinput.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dinput_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dlogic.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dlogic_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dquick.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dquick_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dquickinput.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dquickinput_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dquickrender.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3dquickrender_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3drender.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_3drender_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_axbase.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_axbase_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_axcontainer.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_axcontainer_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_axserver.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_axserver_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_bluetooth.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_bluetooth_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_bootstrap_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_clucene_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_concurrent.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_concurrent_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_core.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_core_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_dbus.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_dbus_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_designer.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_designer_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_designercomponents_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_gui.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_gui_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_help.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_help_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_labscontrols_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_labstemplates_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_location.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_location_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_multimedia.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_multimedia_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_multimediawidgets.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_multimediawidgets_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_network.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_network_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_nfc.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_nfc_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_opengl.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_opengl_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_openglextensions.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_openglextensions_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_platformsupport_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_positioning.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_positioning_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_printsupport.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_printsupport_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_qml.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_qml_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_qmldevtools_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_qmltest.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_qmltest_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_qtmultimediaquicktools_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_quick.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_quick_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_quickparticles_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_quickwidgets.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_quickwidgets_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_script.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_script_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_scripttools.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_scripttools_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_sensors.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_sensors_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_serialbus.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_serialbus_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_serialport.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_serialport_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_sql.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_sql_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_svg.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_svg_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_testlib.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_testlib_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_uiplugin.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_uitools.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_uitools_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_webchannel.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_webchannel_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_websockets.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_websockets_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_widgets.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_widgets_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_winextras.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_winextras_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_xml.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_xml_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_xmlpatterns.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\modules\qt_lib_xmlpatterns_private.pri D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\qt_functions.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\qt_config.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\win32\qt_config.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\win32-g++\qmake.conf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\spec_post.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\exclusive_builds.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\default_pre.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\win32\default_pre.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\resolve_config.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\exclusive_builds_post.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\default_post.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\build_pass.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\qml_debug.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\win32\rtti.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\precompile_header.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\warn_on.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\qt.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\resources.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\moc.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\win32\opengl.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\uic.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\file_copies.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\win32\windows.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\testcase_targets.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\exceptions.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\yacc.prf D:\Qt\Qt5.6.3\5.6.3\mingw49_32\mkspecs\features\lex.prf ..\MonitorWhiteCtrlProgramManage.pro D:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib\qtmaind.prl D:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib\Qt5Widgetsd.prl D:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib\Qt5Guid.prl D:\Qt\Qt5.6.3\5.6.3\mingw49_32\lib\Qt5Cored.prl    ..\DlgMain.h ..\main.cpp ..\DlgMain.cpp ..\DlgMain.ui    

clean: compiler_clean 
	-$(DEL_FILE) debug\main.o debug\DlgMain.o debug\moc_DlgMain.o

distclean: clean 
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Debug

mocclean: compiler_moc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_header_make_all: debug/moc_DlgMain.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) debug\moc_DlgMain.cpp
debug/moc_DlgMain.cpp: D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QDialog \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qdialog.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qline.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qset.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtouchdevice.h \
		../DlgMain.h
	D:\Qt\Qt5.6.3\5.6.3\mingw49_32\bin\moc.exe $(DEFINES) -D__GNUC__ -DWIN32 -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/mkspecs/win32-g++ -IE:/Work/002_Project/001_MonitorWhiteCtrlProgram/001_Code/002_MonitorWhiteCtrlProgramManage -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtANGLE -ID:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore -I. ..\DlgMain.h -o debug\moc_DlgMain.cpp

compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_DlgMain.h
compiler_uic_clean:
	-$(DEL_FILE) ui_DlgMain.h
ui_DlgMain.h: ../DlgMain.ui
	D:\Qt\Qt5.6.3\5.6.3\mingw49_32\bin\uic.exe ..\DlgMain.ui -o ui_DlgMain.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_header_clean compiler_uic_clean 



####### Compile

debug/main.o: ../main.cpp ../DlgMain.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QDialog \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qdialog.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qline.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qset.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtouchdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QApplication \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qeventloop.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qdesktopwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qguiapplication.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qinputmethod.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\main.o ..\main.cpp

debug/DlgMain.o: ../DlgMain.cpp ../DlgMain.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/QDialog \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qdialog.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qwidget.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobal.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qconfig.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfeatures.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsystemdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qprocessordetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcompilerdetection.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypeinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtypetraits.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qisenum.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsysinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlogging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qflags.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbasicatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_bootstrap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qgenericatomic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_cxx11.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_gcc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_msvc.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv7.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv6.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_armv5.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_ia64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_x86.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qatomic_unix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qglobalstatic.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmutex.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnumeric.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qversiontagging.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qnamespace.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobjectdefs_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qwindowdefs_win.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstring.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qchar.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrefcount.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qarraydata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringbuilder.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qalgorithms.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiterator.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhashfunctions.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpair.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qbytearraylist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringlist.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qregexp.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qstringmatcher.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcoreevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qscopedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmetatype.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvarlengtharray.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontainerfwd.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qobject_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmargins.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpaintdevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qrect.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsize.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qpoint.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpalette.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcolor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgb.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qrgba64.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qbrush.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvector.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qmatrix.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpolygon.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qregion.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdatastream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qiodevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qline.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtransform.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpainterpath.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qimage.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixelformat.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qpixmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qshareddata.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qhash.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qsharedpointer_impl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfont.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontmetrics.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qfontinfo.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtWidgets/qsizepolicy.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qcursor.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qkeysequence.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qevent.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qvariant.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qmap.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qdebug.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qtextstream.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qlocale.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qset.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qcontiguouscache.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurl.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qurlquery.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfile.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtCore/qfiledevice.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qvector2d.h \
		D:/Qt/Qt5.6.3/5.6.3/mingw49_32/include/QtGui/qtouchdevice.h \
		ui_DlgMain.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\DlgMain.o ..\DlgMain.cpp

debug/moc_DlgMain.o: debug/moc_DlgMain.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_DlgMain.o debug\moc_DlgMain.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

