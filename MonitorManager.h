#ifndef MONITORMANAGER_H
#define MONITORMANAGER_H

#include <QObject>
#include <QStringList>
#include <QTimer>
#include "EDIDParser.h"

/**
 * @brief 显示器管理器类
 * 管理显示器白名单，监听显示器连接状态
 */
class MonitorManager : public QObject
{
    Q_OBJECT

public:
    explicit MonitorManager(QObject *parent = nullptr);
    ~MonitorManager();

    /**
     * @brief 初始化管理器
     */
    void initialize();

    /**
     * @brief 开始监听显示器状态
     */
    void startMonitoring();

    /**
     * @brief 停止监听显示器状态
     */
    void stopMonitoring();

    /**
     * @brief 获取当前连接的显示器列表
     * @return 显示器EDID信息列表
     */
    QList<EDIDParser::EDIDInfo> getCurrentMonitors();

    /**
     * @brief 获取白名单显示器列表
     * @return 白名单显示器唯一标识列表
     */
    QStringList getWhitelistMonitors();

    /**
     * @brief 添加显示器到白名单
     * @param uniqueId 显示器唯一标识
     * @return 是否添加成功
     */
    bool addToWhitelist(const QString& uniqueId);

    /**
     * @brief 从白名单移除显示器
     * @param uniqueId 显示器唯一标识
     * @return 是否移除成功
     */
    bool removeFromWhitelist(const QString& uniqueId);

    /**
     * @brief 清空白名单
     */
    void clearWhitelist();

    /**
     * @brief 检查显示器是否在白名单中
     * @param uniqueId 显示器唯一标识
     * @return 是否在白名单中
     */
    bool isInWhitelist(const QString& uniqueId);

    /**
     * @brief 获取白名单文件路径
     * @return 文件路径
     */
    QString getWhitelistFilePath() const;

    /**
     * @brief 根据唯一标识获取显示器信息
     * @param uniqueId 唯一标识
     * @return 显示器信息，如果未找到则返回无效信息
     */
    EDIDParser::EDIDInfo getMonitorInfo(const QString& uniqueId);

signals:
    /**
     * @brief 显示器连接信号
     * @param monitorInfo 显示器信息
     */
    void monitorConnected(const EDIDParser::EDIDInfo& monitorInfo);

    /**
     * @brief 显示器断开信号
     * @param monitorInfo 显示器信息
     */
    void monitorDisconnected(const EDIDParser::EDIDInfo& monitorInfo);

    /**
     * @brief 显示器列表更新信号
     */
    void monitorListUpdated();

    /**
     * @brief 白名单更新信号
     */
    void whitelistUpdated();

private slots:
    /**
     * @brief 检查显示器状态
     */
    void checkMonitorStatus();

private:
    /**
     * @brief 加载白名单
     */
    void loadWhitelist();

    /**
     * @brief 保存白名单
     */
    void saveWhitelist();

    /**
     * @brief 比较显示器列表差异
     * @param oldList 旧显示器列表
     * @param newList 新显示器列表
     */
    void compareMonitorLists(const QList<EDIDParser::EDIDInfo>& oldList,
                            const QList<EDIDParser::EDIDInfo>& newList);

    /**
     * @brief 创建配置目录
     */
    void createConfigDirectory();

private:
    QTimer* m_checkTimer;                           // 定时检查器
    QList<EDIDParser::EDIDInfo> m_currentMonitors;  // 当前连接的显示器
    QStringList m_whitelist;                        // 白名单显示器唯一标识列表
    QString m_whitelistFilePath;                    // 白名单文件路径
    bool m_monitoring;                              // 是否正在监听
};

#endif // MONITORMANAGER_H
