#ifndef SYSTEMAPITEST_H
#define SYSTEMAPITEST_H

#include <QObject>
#include <QTimer>
#include <QDebug>
#include "EDIDParser.h"

/**
 * @brief 系统API测试类
 * 用于测试和验证Windows系统API的EDID检测功能
 */
class SystemAPITest : public QObject
{
    Q_OBJECT

public:
    explicit SystemAPITest(QObject *parent = nullptr);
    ~SystemAPITest();

    /**
     * @brief 开始系统API测试
     */
    void startTest();

    /**
     * @brief 停止测试
     */
    void stopTest();

    /**
     * @brief 执行完整的API测试
     */
    void runFullTest();

public slots:
    /**
     * @brief 定时测试槽函数
     */
    void performPeriodicTest();

signals:
    /**
     * @brief 测试结果信号
     * @param testName 测试名称
     * @param success 是否成功
     * @param details 详细信息
     */
    void testResult(const QString& testName, bool success, const QString& details);

    /**
     * @brief EDID数据检测信号
     * @param method 检测方法
     * @param edidCount 检测到的EDID数量
     * @param details 详细信息
     */
    void edidDetected(const QString& method, int edidCount, const QString& details);

private:
    /**
     * @brief 测试SetupAPI方法
     */
    void testSetupAPI();

    /**
     * @brief 测试DisplayAPI方法
     */
    void testDisplayAPI();

    /**
     * @brief 测试注册表方法
     */
    void testRegistryMethod();

    /**
     * @brief 测试EDID解析
     */
    void testEDIDParsing();

    /**
     * @brief 输出测试统计
     */
    void outputTestStatistics();

private:
    QTimer* m_testTimer;
    int m_testCount;
    int m_successCount;
    int m_totalEDIDFound;
    bool m_running;
};

#endif // SYSTEMAPITEST_H
