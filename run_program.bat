@echo off
echo ========================================
echo 显示器白名单控制程序 - 多线程版本
echo ========================================
echo.

REM 设置Qt环境变量
echo 设置Qt环境变量...
set PATH=D:\Qt\Qt5.6.3\5.6.3\mingw49_32\bin;D:\Qt\Qt5.6.3\Tools\mingw492_32\bin;%PATH%

REM 切换到构建目录
echo 切换到构建目录...
cd build-MonitorWhiteCtrlProgramManage-Desktop_Qt_5_6_3_MinGW_32bit-Debug

REM 检查程序文件是否存在
if not exist "debug\MonitorWhiteCtrlProgramManage.exe" (
    echo 错误：程序文件不存在！
    echo 请先编译程序。
    pause
    exit /b 1
)

echo 启动程序...
echo.
echo 功能说明：
echo - 多线程每1秒检测显示器EDID数据
echo - 实时生成显示器唯一标识
echo - 支持USB Key插拔监听
echo - 显示器白名单管理
echo - 安全的线程退出机制
echo.

REM 运行程序
debug\MonitorWhiteCtrlProgramManage.exe

echo.
echo 程序已退出
echo 如果程序正常退出，说明崩溃问题已修复！
pause
