#include "MonitorDetectionThread.h"
#include <QDebug>
#include <QApplication>
#include <QMutexLocker>

MonitorDetectionThread::MonitorDetectionThread(QObject *parent)
    : QThread(parent)
    , m_detectionTimer(nullptr)
    , m_running(false)
    , m_stopRequested(false)
    , m_detectionInterval(1000)  // 默认1秒间隔
    , m_detectionCount(0)
{
    // 在主线程中创建定时器
    m_detectionTimer = new QTimer();
    m_detectionTimer->setSingleShot(false);
    m_detectionTimer->setInterval(m_detectionInterval);
    
    // 连接定时器信号到检测槽函数
    connect(m_detectionTimer, &QTimer::timeout, this, &MonitorDetectionThread::performDetection);
    
    // 将定时器移动到工作线程
    m_detectionTimer->moveToThread(this);
}

MonitorDetectionThread::~MonitorDetectionThread()
{
    stopDetection();

    // 确保线程完全停止
    if (isRunning()) {
        terminate();
        wait(1000);
    }

    // 清理定时器
    if (m_detectionTimer) {
        if (m_detectionTimer->thread() == this) {
            // 如果定时器在工作线程中，直接删除
            delete m_detectionTimer;
        } else {
            // 如果定时器在主线程中，使用deleteLater
            m_detectionTimer->deleteLater();
        }
        m_detectionTimer = nullptr;
    }
}

void MonitorDetectionThread::startDetection()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_running) {
        qDebug() << "显示器检测线程已在运行";
        return;
    }
    
    m_stopRequested = false;
    m_detectionCount = 0;
    
    qDebug() << "启动显示器检测线程，检测间隔:" << m_detectionInterval << "ms";
    
    // 启动线程
    start();
}

void MonitorDetectionThread::stopDetection()
{
    {
        QMutexLocker locker(&m_mutex);
        if (!m_running) {
            return;
        }

        m_stopRequested = true;
        m_condition.wakeAll();
    }

    qDebug() << "停止显示器检测线程...";

    // 停止定时器
    if (m_detectionTimer && m_detectionTimer->isActive()) {
        QMetaObject::invokeMethod(m_detectionTimer, "stop", Qt::QueuedConnection);
    }

    // 退出事件循环
    quit();

    // 等待线程结束
    if (!wait(3000)) {
        qDebug() << "强制终止显示器检测线程";
        terminate();
        wait(1000);
    }

    qDebug() << "显示器检测线程已停止";
}

QList<EDIDParser::EDIDInfo> MonitorDetectionThread::getCurrentMonitors() const
{
    QMutexLocker locker(&m_mutex);
    return m_currentMonitors;
}

bool MonitorDetectionThread::isDetectionRunning() const
{
    QMutexLocker locker(&m_mutex);
    return m_running;
}

void MonitorDetectionThread::setDetectionInterval(int intervalMs)
{
    QMutexLocker locker(&m_mutex);
    
    if (intervalMs < 100) {
        intervalMs = 100; // 最小100ms间隔
    }
    
    m_detectionInterval = intervalMs;
    
    if (m_detectionTimer) {
        m_detectionTimer->setInterval(m_detectionInterval);
    }
    
    qDebug() << "设置检测间隔为:" << m_detectionInterval << "ms";
}

void MonitorDetectionThread::run()
{
    {
        QMutexLocker locker(&m_mutex);
        m_running = true;
    }
    
    qDebug() << "显示器检测线程开始运行";
    emit detectionStatusUpdated(true, 0);
    
    try {
        // 启动定时器
        QMetaObject::invokeMethod(m_detectionTimer, "start", Qt::QueuedConnection);
        
        // 立即执行一次检测
        QMetaObject::invokeMethod(this, "performDetection", Qt::QueuedConnection);
        
        // 进入事件循环
        exec();
        
    } catch (const std::exception& e) {
        qDebug() << "显示器检测线程异常:" << e.what();
        emit errorOccurred(QString("检测线程异常: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "显示器检测线程未知异常";
        emit errorOccurred("检测线程未知异常");
    }
    
    // 停止定时器
    if (m_detectionTimer) {
        m_detectionTimer->stop();
        // 断开所有信号连接，避免退出时的信号问题
        m_detectionTimer->disconnect();
    }

    {
        QMutexLocker locker(&m_mutex);
        m_running = false;
    }

    qDebug() << "显示器检测线程结束运行";

    // 发送最后的状态更新信号
    try {
        emit detectionStatusUpdated(false, 0);
    } catch (...) {
        // 忽略退出时的信号异常
    }
}

void MonitorDetectionThread::performDetection()
{
    {
        QMutexLocker locker(&m_mutex);
        if (m_stopRequested) {
            quit();
            return;
        }
    }
    
    try {
        // 获取当前连接的显示器
        QList<EDIDParser::EDIDInfo> newMonitors = EDIDParser::getAllConnectedMonitors();
        
        {
            QMutexLocker locker(&m_mutex);
            m_detectionCount++;
            
            // 比较显示器列表变化
            compareMonitorLists(m_currentMonitors, newMonitors);
            
            // 更新当前显示器列表
            m_currentMonitors = newMonitors;
        }
        
        // 发送更新信号
        emit monitorsUpdated(newMonitors);
        emit detectionStatusUpdated(true, newMonitors.size());
        
        // 每10次检测输出一次日志
        if (m_detectionCount % 10 == 0) {
            qDebug() << QString("显示器检测第%1次，发现%2个显示器")
                        .arg(m_detectionCount)
                        .arg(newMonitors.size());
        }
        
    } catch (const std::exception& e) {
        qDebug() << "显示器检测异常:" << e.what();
        emit errorOccurred(QString("显示器检测异常: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "显示器检测未知异常";
        emit errorOccurred("显示器检测未知异常");
    }
}

void MonitorDetectionThread::compareMonitorLists(const QList<EDIDParser::EDIDInfo>& oldList,
                                                 const QList<EDIDParser::EDIDInfo>& newList)
{
    // 检查新连接的显示器
    foreach (const EDIDParser::EDIDInfo& newMonitor, newList) {
        if (findMonitorIndex(oldList, newMonitor.uniqueId) == -1) {
            qDebug() << "检测到新显示器:" << newMonitor.getDisplayString();
            emit monitorDetected(newMonitor);
        }
    }
    
    // 检查断开的显示器
    foreach (const EDIDParser::EDIDInfo& oldMonitor, oldList) {
        if (findMonitorIndex(newList, oldMonitor.uniqueId) == -1) {
            qDebug() << "显示器断开:" << oldMonitor.getDisplayString();
            emit monitorLost(oldMonitor);
        }
    }
}

int MonitorDetectionThread::findMonitorIndex(const QList<EDIDParser::EDIDInfo>& monitors, 
                                            const QString& uniqueId) const
{
    for (int i = 0; i < monitors.size(); ++i) {
        if (monitors[i].uniqueId == uniqueId) {
            return i;
        }
    }
    return -1;
}

void MonitorDetectionThread::updateMonitorList(const QList<EDIDParser::EDIDInfo>& newMonitors)
{
    QMutexLocker locker(&m_mutex);
    m_currentMonitors = newMonitors;
}
