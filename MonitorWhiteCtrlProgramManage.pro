#-------------------------------------------------
#
# Project created by QtCreator 2025-05-29T15:09:45
#
#-------------------------------------------------

QT       += core gui widgets

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

TARGET = MonitorWhiteCtrlProgramManage
TEMPLATE = app

# 启用控制台输出用于调试
CONFIG += console

# Windows specific libraries for USB monitoring and display detection
win32 {
    LIBS += -luser32 -lsetupapi -lgdi32 -lcfgmgr32 -ladvapi32
}

SOURCES += main.cpp\
        DlgMain.cpp \
        USBMonitor.cpp \
        MonitorManager.cpp \
        EDIDParser.cpp \
        MonitorDetectionThread.cpp

HEADERS  += DlgMain.h \
        USBMonitor.h \
        MonitorManager.h \
        EDIDParser.h \
        MonitorDetectionThread.h

FORMS    += DlgMain.ui
