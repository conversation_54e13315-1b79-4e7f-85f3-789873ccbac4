#ifndef MONITORDETECTIONTHREAD_H
#define MONITORDETECTIONTHREAD_H

#include <QThread>
#include <QMutex>
#include <QWaitCondition>
#include <QTimer>
#include <QList>
#include "EDIDParser.h"

/**
 * @brief 显示器检测线程类
 * 在独立线程中每秒检测显示器EDID数据并生成唯一标识
 */
class MonitorDetectionThread : public QThread
{
    Q_OBJECT

public:
    explicit MonitorDetectionThread(QObject *parent = nullptr);
    virtual ~MonitorDetectionThread();

    /**
     * @brief 启动监测线程
     */
    void startDetection();

    /**
     * @brief 停止监测线程
     */
    void stopDetection();

    /**
     * @brief 获取当前检测到的显示器列表
     * @return 显示器信息列表
     */
    QList<EDIDParser::EDIDInfo> getCurrentMonitors() const;

    /**
     * @brief 检查线程是否正在运行
     * @return 是否正在运行
     */
    bool isDetectionRunning() const;

    /**
     * @brief 设置检测间隔
     * @param intervalMs 间隔时间（毫秒）
     */
    void setDetectionInterval(int intervalMs);

signals:
    /**
     * @brief 检测到新的显示器信号
     * @param monitorInfo 显示器信息
     */
    void monitorDetected(const EDIDParser::EDIDInfo& monitorInfo);

    /**
     * @brief 显示器断开连接信号
     * @param monitorInfo 显示器信息
     */
    void monitorLost(const EDIDParser::EDIDInfo& monitorInfo);

    /**
     * @brief 显示器列表更新信号
     * @param monitors 当前所有显示器列表
     */
    void monitorsUpdated(const QList<EDIDParser::EDIDInfo>& monitors);

    /**
     * @brief 检测状态更新信号
     * @param isRunning 是否正在运行
     * @param monitorCount 当前显示器数量
     */
    void detectionStatusUpdated(bool isRunning, int monitorCount);

    /**
     * @brief 错误信号
     * @param errorMessage 错误信息
     */
    void errorOccurred(const QString& errorMessage);

protected:
    /**
     * @brief 线程主函数
     */
    void run() override;

private slots:
    /**
     * @brief 执行显示器检测
     */
    void performDetection();

private:
    /**
     * @brief 比较显示器列表差异
     * @param oldList 旧列表
     * @param newList 新列表
     */
    void compareMonitorLists(const QList<EDIDParser::EDIDInfo>& oldList,
                            const QList<EDIDParser::EDIDInfo>& newList);

    /**
     * @brief 查找显示器在列表中的位置
     * @param monitors 显示器列表
     * @param uniqueId 唯一标识
     * @return 位置索引，-1表示未找到
     */
    int findMonitorIndex(const QList<EDIDParser::EDIDInfo>& monitors, 
                        const QString& uniqueId) const;

    /**
     * @brief 线程安全地更新显示器列表
     * @param newMonitors 新的显示器列表
     */
    void updateMonitorList(const QList<EDIDParser::EDIDInfo>& newMonitors);

private:
    mutable QMutex m_mutex;                         // 线程同步互斥锁
    QWaitCondition m_condition;                     // 等待条件
    QTimer* m_detectionTimer;                       // 检测定时器
    QList<EDIDParser::EDIDInfo> m_currentMonitors;  // 当前显示器列表
    bool m_running;                                 // 运行状态
    bool m_stopRequested;                           // 停止请求标志
    int m_detectionInterval;                        // 检测间隔（毫秒）
    int m_detectionCount;                           // 检测次数计数器
};

#endif // MONITORDETECTIONTHREAD_H
