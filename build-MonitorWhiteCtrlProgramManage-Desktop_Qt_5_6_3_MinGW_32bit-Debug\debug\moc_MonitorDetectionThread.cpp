/****************************************************************************
** Meta object code from reading C++ file 'MonitorDetectionThread.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../MonitorDetectionThread.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#include <QtCore/QList>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'MonitorDetectionThread.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
struct qt_meta_stringdata_MonitorDetectionThread_t {
    QByteArrayData data[15];
    char stringdata0[228];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_MonitorDetectionThread_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_MonitorDetectionThread_t qt_meta_stringdata_MonitorDetectionThread = {
    {
QT_MOC_LITERAL(0, 0, 22), // "MonitorDetectionThread"
QT_MOC_LITERAL(1, 23, 15), // "monitorDetected"
QT_MOC_LITERAL(2, 39, 0), // ""
QT_MOC_LITERAL(3, 40, 20), // "EDIDParser::EDIDInfo"
QT_MOC_LITERAL(4, 61, 11), // "monitorInfo"
QT_MOC_LITERAL(5, 73, 11), // "monitorLost"
QT_MOC_LITERAL(6, 85, 15), // "monitorsUpdated"
QT_MOC_LITERAL(7, 101, 27), // "QList<EDIDParser::EDIDInfo>"
QT_MOC_LITERAL(8, 129, 8), // "monitors"
QT_MOC_LITERAL(9, 138, 22), // "detectionStatusUpdated"
QT_MOC_LITERAL(10, 161, 9), // "isRunning"
QT_MOC_LITERAL(11, 171, 12), // "monitorCount"
QT_MOC_LITERAL(12, 184, 13), // "errorOccurred"
QT_MOC_LITERAL(13, 198, 12), // "errorMessage"
QT_MOC_LITERAL(14, 211, 16) // "performDetection"

    },
    "MonitorDetectionThread\0monitorDetected\0"
    "\0EDIDParser::EDIDInfo\0monitorInfo\0"
    "monitorLost\0monitorsUpdated\0"
    "QList<EDIDParser::EDIDInfo>\0monitors\0"
    "detectionStatusUpdated\0isRunning\0"
    "monitorCount\0errorOccurred\0errorMessage\0"
    "performDetection"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_MonitorDetectionThread[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       6,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   44,    2, 0x06 /* Public */,
       5,    1,   47,    2, 0x06 /* Public */,
       6,    1,   50,    2, 0x06 /* Public */,
       9,    2,   53,    2, 0x06 /* Public */,
      12,    1,   58,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      14,    0,   61,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 7,    8,
    QMetaType::Void, QMetaType::Bool, QMetaType::Int,   10,   11,
    QMetaType::Void, QMetaType::QString,   13,

 // slots: parameters
    QMetaType::Void,

       0        // eod
};

void MonitorDetectionThread::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        MonitorDetectionThread *_t = static_cast<MonitorDetectionThread *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->monitorDetected((*reinterpret_cast< const EDIDParser::EDIDInfo(*)>(_a[1]))); break;
        case 1: _t->monitorLost((*reinterpret_cast< const EDIDParser::EDIDInfo(*)>(_a[1]))); break;
        case 2: _t->monitorsUpdated((*reinterpret_cast< const QList<EDIDParser::EDIDInfo>(*)>(_a[1]))); break;
        case 3: _t->detectionStatusUpdated((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 4: _t->errorOccurred((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 5: _t->performDetection(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        void **func = reinterpret_cast<void **>(_a[1]);
        {
            typedef void (MonitorDetectionThread::*_t)(const EDIDParser::EDIDInfo & );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&MonitorDetectionThread::monitorDetected)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (MonitorDetectionThread::*_t)(const EDIDParser::EDIDInfo & );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&MonitorDetectionThread::monitorLost)) {
                *result = 1;
                return;
            }
        }
        {
            typedef void (MonitorDetectionThread::*_t)(const QList<EDIDParser::EDIDInfo> & );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&MonitorDetectionThread::monitorsUpdated)) {
                *result = 2;
                return;
            }
        }
        {
            typedef void (MonitorDetectionThread::*_t)(bool , int );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&MonitorDetectionThread::detectionStatusUpdated)) {
                *result = 3;
                return;
            }
        }
        {
            typedef void (MonitorDetectionThread::*_t)(const QString & );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&MonitorDetectionThread::errorOccurred)) {
                *result = 4;
                return;
            }
        }
    }
}

const QMetaObject MonitorDetectionThread::staticMetaObject = {
    { &QThread::staticMetaObject, qt_meta_stringdata_MonitorDetectionThread.data,
      qt_meta_data_MonitorDetectionThread,  qt_static_metacall, Q_NULLPTR, Q_NULLPTR}
};


const QMetaObject *MonitorDetectionThread::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MonitorDetectionThread::qt_metacast(const char *_clname)
{
    if (!_clname) return Q_NULLPTR;
    if (!strcmp(_clname, qt_meta_stringdata_MonitorDetectionThread.stringdata0))
        return static_cast<void*>(const_cast< MonitorDetectionThread*>(this));
    return QThread::qt_metacast(_clname);
}

int MonitorDetectionThread::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QThread::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 6;
    }
    return _id;
}

// SIGNAL 0
void MonitorDetectionThread::monitorDetected(const EDIDParser::EDIDInfo & _t1)
{
    void *_a[] = { Q_NULLPTR, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void MonitorDetectionThread::monitorLost(const EDIDParser::EDIDInfo & _t1)
{
    void *_a[] = { Q_NULLPTR, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void MonitorDetectionThread::monitorsUpdated(const QList<EDIDParser::EDIDInfo> & _t1)
{
    void *_a[] = { Q_NULLPTR, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void MonitorDetectionThread::detectionStatusUpdated(bool _t1, int _t2)
{
    void *_a[] = { Q_NULLPTR, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void MonitorDetectionThread::errorOccurred(const QString & _t1)
{
    void *_a[] = { Q_NULLPTR, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}
QT_END_MOC_NAMESPACE
