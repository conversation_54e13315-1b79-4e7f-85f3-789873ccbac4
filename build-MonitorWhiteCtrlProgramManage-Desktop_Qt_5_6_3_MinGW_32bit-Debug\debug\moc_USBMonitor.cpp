/****************************************************************************
** Meta object code from reading C++ file 'USBMonitor.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../USBMonitor.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'USBMonitor.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
struct qt_meta_stringdata_USBMonitor_t {
    QByteArrayData data[9];
    char stringdata0[109];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_USBMonitor_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_USBMonitor_t qt_meta_stringdata_USBMonitor = {
    {
QT_MOC_LITERAL(0, 0, 10), // "USBMonitor"
QT_MOC_LITERAL(1, 11, 14), // "usbKeyInserted"
QT_MOC_LITERAL(2, 26, 0), // ""
QT_MOC_LITERAL(3, 27, 13), // "USBDeviceInfo"
QT_MOC_LITERAL(4, 41, 10), // "deviceInfo"
QT_MOC_LITERAL(5, 52, 13), // "usbKeyRemoved"
QT_MOC_LITERAL(6, 66, 16), // "usbStatusChanged"
QT_MOC_LITERAL(7, 83, 9), // "hasUSBKey"
QT_MOC_LITERAL(8, 93, 15) // "checkUSBDevices"

    },
    "USBMonitor\0usbKeyInserted\0\0USBDeviceInfo\0"
    "deviceInfo\0usbKeyRemoved\0usbStatusChanged\0"
    "hasUSBKey\0checkUSBDevices"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_USBMonitor[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       4,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   34,    2, 0x06 /* Public */,
       5,    1,   37,    2, 0x06 /* Public */,
       6,    1,   40,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       8,    0,   43,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, QMetaType::Bool,    7,

 // slots: parameters
    QMetaType::Void,

       0        // eod
};

void USBMonitor::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        USBMonitor *_t = static_cast<USBMonitor *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->usbKeyInserted((*reinterpret_cast< const USBDeviceInfo(*)>(_a[1]))); break;
        case 1: _t->usbKeyRemoved((*reinterpret_cast< const USBDeviceInfo(*)>(_a[1]))); break;
        case 2: _t->usbStatusChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 3: _t->checkUSBDevices(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        void **func = reinterpret_cast<void **>(_a[1]);
        {
            typedef void (USBMonitor::*_t)(const USBDeviceInfo & );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&USBMonitor::usbKeyInserted)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (USBMonitor::*_t)(const USBDeviceInfo & );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&USBMonitor::usbKeyRemoved)) {
                *result = 1;
                return;
            }
        }
        {
            typedef void (USBMonitor::*_t)(bool );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&USBMonitor::usbStatusChanged)) {
                *result = 2;
                return;
            }
        }
    }
}

const QMetaObject USBMonitor::staticMetaObject = {
    { &QObject::staticMetaObject, qt_meta_stringdata_USBMonitor.data,
      qt_meta_data_USBMonitor,  qt_static_metacall, Q_NULLPTR, Q_NULLPTR}
};


const QMetaObject *USBMonitor::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *USBMonitor::qt_metacast(const char *_clname)
{
    if (!_clname) return Q_NULLPTR;
    if (!strcmp(_clname, qt_meta_stringdata_USBMonitor.stringdata0))
        return static_cast<void*>(const_cast< USBMonitor*>(this));
    return QObject::qt_metacast(_clname);
}

int USBMonitor::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 4)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 4;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 4)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 4;
    }
    return _id;
}

// SIGNAL 0
void USBMonitor::usbKeyInserted(const USBDeviceInfo & _t1)
{
    void *_a[] = { Q_NULLPTR, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void USBMonitor::usbKeyRemoved(const USBDeviceInfo & _t1)
{
    void *_a[] = { Q_NULLPTR, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void USBMonitor::usbStatusChanged(bool _t1)
{
    void *_a[] = { Q_NULLPTR, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
QT_END_MOC_NAMESPACE
