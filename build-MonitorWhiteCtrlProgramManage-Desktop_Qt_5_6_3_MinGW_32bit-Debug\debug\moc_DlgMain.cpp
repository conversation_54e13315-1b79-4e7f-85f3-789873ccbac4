/****************************************************************************
** Meta object code from reading C++ file 'DlgMain.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../DlgMain.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'DlgMain.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
struct qt_meta_stringdata_CDlgMain_t {
    QByteArrayData data[21];
    char stringdata0[385];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CDlgMain_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CDlgMain_t qt_meta_stringdata_CDlgMain = {
    {
QT_MOC_LITERAL(0, 0, 8), // "CDlgMain"
QT_MOC_LITERAL(1, 9, 16), // "onUSBKeyInserted"
QT_MOC_LITERAL(2, 26, 0), // ""
QT_MOC_LITERAL(3, 27, 25), // "USBMonitor::USBDeviceInfo"
QT_MOC_LITERAL(4, 53, 10), // "deviceInfo"
QT_MOC_LITERAL(5, 64, 15), // "onUSBKeyRemoved"
QT_MOC_LITERAL(6, 80, 18), // "onUSBStatusChanged"
QT_MOC_LITERAL(7, 99, 9), // "hasUSBKey"
QT_MOC_LITERAL(8, 109, 18), // "onMonitorConnected"
QT_MOC_LITERAL(9, 128, 20), // "EDIDParser::EDIDInfo"
QT_MOC_LITERAL(10, 149, 11), // "monitorInfo"
QT_MOC_LITERAL(11, 161, 21), // "onMonitorDisconnected"
QT_MOC_LITERAL(12, 183, 20), // "onMonitorListUpdated"
QT_MOC_LITERAL(13, 204, 18), // "onWhitelistUpdated"
QT_MOC_LITERAL(14, 223, 23), // "onAddToWhitelistClicked"
QT_MOC_LITERAL(15, 247, 28), // "onRemoveFromWhitelistClicked"
QT_MOC_LITERAL(16, 276, 23), // "onClearWhitelistClicked"
QT_MOC_LITERAL(17, 300, 33), // "onCurrentMonitorItemDoubleCli..."
QT_MOC_LITERAL(18, 334, 16), // "QListWidgetItem*"
QT_MOC_LITERAL(19, 351, 4), // "item"
QT_MOC_LITERAL(20, 356, 28) // "onWhitelistItemDoubleClicked"

    },
    "CDlgMain\0onUSBKeyInserted\0\0"
    "USBMonitor::USBDeviceInfo\0deviceInfo\0"
    "onUSBKeyRemoved\0onUSBStatusChanged\0"
    "hasUSBKey\0onMonitorConnected\0"
    "EDIDParser::EDIDInfo\0monitorInfo\0"
    "onMonitorDisconnected\0onMonitorListUpdated\0"
    "onWhitelistUpdated\0onAddToWhitelistClicked\0"
    "onRemoveFromWhitelistClicked\0"
    "onClearWhitelistClicked\0"
    "onCurrentMonitorItemDoubleClicked\0"
    "QListWidgetItem*\0item\0"
    "onWhitelistItemDoubleClicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CDlgMain[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
      12,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    1,   74,    2, 0x08 /* Private */,
       5,    1,   77,    2, 0x08 /* Private */,
       6,    1,   80,    2, 0x08 /* Private */,
       8,    1,   83,    2, 0x08 /* Private */,
      11,    1,   86,    2, 0x08 /* Private */,
      12,    0,   89,    2, 0x08 /* Private */,
      13,    0,   90,    2, 0x08 /* Private */,
      14,    0,   91,    2, 0x08 /* Private */,
      15,    0,   92,    2, 0x08 /* Private */,
      16,    0,   93,    2, 0x08 /* Private */,
      17,    1,   94,    2, 0x08 /* Private */,
      20,    1,   97,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, QMetaType::Bool,    7,
    QMetaType::Void, 0x80000000 | 9,   10,
    QMetaType::Void, 0x80000000 | 9,   10,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 18,   19,
    QMetaType::Void, 0x80000000 | 18,   19,

       0        // eod
};

void CDlgMain::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        CDlgMain *_t = static_cast<CDlgMain *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onUSBKeyInserted((*reinterpret_cast< const USBMonitor::USBDeviceInfo(*)>(_a[1]))); break;
        case 1: _t->onUSBKeyRemoved((*reinterpret_cast< const USBMonitor::USBDeviceInfo(*)>(_a[1]))); break;
        case 2: _t->onUSBStatusChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 3: _t->onMonitorConnected((*reinterpret_cast< const EDIDParser::EDIDInfo(*)>(_a[1]))); break;
        case 4: _t->onMonitorDisconnected((*reinterpret_cast< const EDIDParser::EDIDInfo(*)>(_a[1]))); break;
        case 5: _t->onMonitorListUpdated(); break;
        case 6: _t->onWhitelistUpdated(); break;
        case 7: _t->onAddToWhitelistClicked(); break;
        case 8: _t->onRemoveFromWhitelistClicked(); break;
        case 9: _t->onClearWhitelistClicked(); break;
        case 10: _t->onCurrentMonitorItemDoubleClicked((*reinterpret_cast< QListWidgetItem*(*)>(_a[1]))); break;
        case 11: _t->onWhitelistItemDoubleClicked((*reinterpret_cast< QListWidgetItem*(*)>(_a[1]))); break;
        default: ;
        }
    }
}

const QMetaObject CDlgMain::staticMetaObject = {
    { &QDialog::staticMetaObject, qt_meta_stringdata_CDlgMain.data,
      qt_meta_data_CDlgMain,  qt_static_metacall, Q_NULLPTR, Q_NULLPTR}
};


const QMetaObject *CDlgMain::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CDlgMain::qt_metacast(const char *_clname)
{
    if (!_clname) return Q_NULLPTR;
    if (!strcmp(_clname, qt_meta_stringdata_CDlgMain.stringdata0))
        return static_cast<void*>(const_cast< CDlgMain*>(this));
    return QDialog::qt_metacast(_clname);
}

int CDlgMain::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 12;
    }
    return _id;
}
QT_END_MOC_NAMESPACE
