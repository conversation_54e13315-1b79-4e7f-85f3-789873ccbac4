#ifndef USBMONITOR_H
#define USBMONITOR_H

#include <QObject>
#include <QTimer>
#include <QStringList>

#ifdef Q_OS_WIN
#include <windows.h>
#include <dbt.h>
#endif

/**
 * @brief USB设备监听器类
 * 监听USB设备的插入和拔出事件
 */
class USBMonitor : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief USB设备信息结构
     */
    struct USBDeviceInfo
    {
        QString deviceId;       // 设备ID
        QString deviceName;     // 设备名称
        QString driveLetter;    // 驱动器盘符
        bool isUSBKey;          // 是否为USB Key
        
        USBDeviceInfo() : isUSBKey(false) {}
        
        bool isValid() const {
            return !deviceId.isEmpty();
        }
        
        QString getDisplayString() const {
            return QString("%1 (%2)").arg(deviceName).arg(driveLetter);
        }
    };

public:
    explicit USBMonitor(QObject *parent = nullptr);
    ~USBMonitor();

    /**
     * @brief 开始监听USB设备
     */
    void startMonitoring();

    /**
     * @brief 停止监听USB设备
     */
    void stopMonitoring();

    /**
     * @brief 获取当前连接的USB Key列表
     * @return USB Key设备列表
     */
    QList<USBDeviceInfo> getCurrentUSBKeys();

    /**
     * @brief 检查是否有USB Key插入
     * @return 是否有USB Key
     */
    bool hasUSBKeyConnected();

signals:
    /**
     * @brief USB Key插入信号
     * @param deviceInfo 设备信息
     */
    void usbKeyInserted(const USBDeviceInfo& deviceInfo);

    /**
     * @brief USB Key拔出信号
     * @param deviceInfo 设备信息
     */
    void usbKeyRemoved(const USBDeviceInfo& deviceInfo);

    /**
     * @brief USB状态改变信号
     * @param hasUSBKey 是否有USB Key连接
     */
    void usbStatusChanged(bool hasUSBKey);

protected:
#ifdef Q_OS_WIN
    /**
     * @brief Windows消息处理
     */
    bool nativeEventFilter(const QByteArray &eventType, void *message, long *result);
#endif

private slots:
    /**
     * @brief 定时检查USB设备状态
     */
    void checkUSBDevices();

private:
    /**
     * @brief 扫描USB设备
     * @return USB设备列表
     */
    QList<USBDeviceInfo> scanUSBDevices();

    /**
     * @brief 检查设备是否为USB Key
     * @param driveLetter 驱动器盘符
     * @return 是否为USB Key
     */
    bool isUSBKey(const QString& driveLetter);

    /**
     * @brief 获取设备名称
     * @param driveLetter 驱动器盘符
     * @return 设备名称
     */
    QString getDeviceName(const QString& driveLetter);

    /**
     * @brief 比较设备列表差异
     * @param oldList 旧设备列表
     * @param newList 新设备列表
     */
    void compareDeviceLists(const QList<USBDeviceInfo>& oldList, 
                           const QList<USBDeviceInfo>& newList);

private:
    QTimer* m_checkTimer;                    // 定时检查器
    QList<USBDeviceInfo> m_currentDevices;   // 当前设备列表
    bool m_monitoring;                       // 是否正在监听
    bool m_lastUSBKeyStatus;                 // 上次USB Key状态

#ifdef Q_OS_WIN
    HWND m_hwnd;                            // Windows窗口句柄
#endif
};

#endif // USBMONITOR_H
