#include <QApplication>
#include <QDebug>
#include <QMessageBox>
#include <iostream>
#include "EDIDParser.h"
#include "USBMonitor.h"
#include "MonitorManager.h"

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);
    
    std::cout << "=== 显示器白名单控制程序测试 ===" << std::endl;
    qDebug() << "程序启动...";
    
    try {
        // 测试EDID解析器
        std::cout << "\n1. 测试EDID解析器..." << std::endl;
        QList<EDIDParser::EDIDInfo> monitors = EDIDParser::getAllConnectedMonitors();
        std::cout << "发现 " << monitors.size() << " 个显示器" << std::endl;
        
        foreach (const EDIDParser::EDIDInfo& monitor, monitors) {
            std::cout << "显示器: " << monitor.getDisplayString().toStdString() << std::endl;
            std::cout << "  制造商: " << monitor.manufacturer.toStdString() << std::endl;
            std::cout << "  型号: " << monitor.modelName.toStdString() << std::endl;
            std::cout << "  唯一标识: " << monitor.uniqueId.toStdString() << std::endl;
            std::cout << "  尺寸: " << monitor.width << "x" << monitor.height << "mm" << std::endl;
        }
        
        // 测试USB监听器
        std::cout << "\n2. 测试USB监听器..." << std::endl;
        USBMonitor usbMonitor;
        QList<USBMonitor::USBDeviceInfo> usbDevices = usbMonitor.getCurrentUSBKeys();
        std::cout << "发现 " << usbDevices.size() << " 个USB Key" << std::endl;
        
        foreach (const USBMonitor::USBDeviceInfo& device, usbDevices) {
            std::cout << "USB Key: " << device.getDisplayString().toStdString() << std::endl;
        }
        
        // 测试显示器管理器
        std::cout << "\n3. 测试显示器管理器..." << std::endl;
        MonitorManager monitorManager;
        monitorManager.initialize();
        
        QStringList whitelist = monitorManager.getWhitelistMonitors();
        std::cout << "白名单中有 " << whitelist.size() << " 个显示器" << std::endl;
        
        foreach (const QString& id, whitelist) {
            std::cout << "白名单显示器: " << id.toStdString() << std::endl;
        }
        
        std::cout << "\n测试完成！按任意键继续..." << std::endl;
        std::cin.get();
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "程序异常: " << e.what() << std::endl;
        qDebug() << "程序异常:" << e.what();
        return -1;
    } catch (...) {
        std::cout << "未知异常" << std::endl;
        qDebug() << "未知异常";
        return -1;
    }
}
