#include "EDIDParser.h"
#include <QDebug>
#include <QCryptographicHash>
#include <QSettings>
#include <QDir>

#ifdef Q_OS_WIN
#include <windows.h>
#include <setupapi.h>
#include <cfgmgr32.h>

// 定义显示器设备类GUID
const GUID GUID_DEVCLASS_MONITOR = {0x4d36e96e, 0xe325, 0x11ce, {0xbf, 0xc1, 0x08, 0x00, 0x2b, 0xe1, 0x03, 0x18}};
#endif

EDIDParser::EDIDParser()
{
}

EDIDParser::~EDIDParser()
{
}

EDIDParser::EDIDInfo EDIDParser::parseEDID(const QByteArray& edidData)
{
    EDIDInfo info;

    if (!isValidEDID(edidData)) {
        qDebug() << "无效的EDID数据:" << getEDIDLengthInfo(edidData);
        return info;
    }

    qDebug() << "解析EDID数据:" << getEDIDLengthInfo(edidData);

    info.rawData = edidData;
    info.manufacturer = parseManufacturer(edidData);
    info.productCode = parseProductCode(edidData);
    info.serialNumber = parseSerialNumber(edidData);
    info.modelName = parseModelName(edidData);
    parseScreenSize(edidData, info.width, info.height);

    // 生成唯一标识
    info.uniqueId = generateUniqueId(info);

    qDebug() << QString("EDID解析完成: %1 %2 (ID: %3)")
                .arg(info.manufacturer)
                .arg(info.modelName)
                .arg(info.uniqueId);

    return info;
}

QString EDIDParser::generateUniqueId(const EDIDInfo& edidInfo)
{
    // 使用制造商、产品代码、序列号生成MD5哈希作为唯一标识
    QString combined = QString("%1_%2_%3")
                      .arg(edidInfo.manufacturer)
                      .arg(edidInfo.productCode)
                      .arg(edidInfo.serialNumber);
    
    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(combined.toUtf8());
    return hash.result().toHex().left(16); // 取前16位作为唯一标识
}

QList<EDIDParser::EDIDInfo> EDIDParser::getAllConnectedMonitors()
{
    QList<EDIDInfo> monitors;

#ifdef Q_OS_WIN
    // 使用多种方法获取EDID数据，确保完整性
    QList<QByteArray> edidDataList;

    qDebug() << "开始使用系统API直接获取EDID数据...";

    // 方法1：使用Display API直接访问
    QList<QByteArray> displayApiData = getEDIDFromDisplayAPI();
    edidDataList.append(displayApiData);
    qDebug() << QString("Display API获取到 %1 个EDID数据").arg(displayApiData.size());

    // 方法2：使用SetupAPI枚举设备
    QList<QByteArray> setupApiData = getEDIDFromSetupAPI();
    edidDataList.append(setupApiData);
    qDebug() << QString("SetupAPI获取到 %1 个EDID数据").arg(setupApiData.size());

    // 方法3：使用WMI接口
    QList<QByteArray> wmiData = getEDIDFromWMI();
    edidDataList.append(wmiData);
    qDebug() << QString("WMI获取到 %1 个EDID数据").arg(wmiData.size());

    // 去重并解析EDID数据
    QStringList processedIds;
    foreach (const QByteArray& edidData, edidDataList) {
        if (isValidEDID(edidData)) {
            EDIDInfo info = parseEDID(edidData);
            if (info.isValid() && !processedIds.contains(info.uniqueId)) {
                monitors.append(info);
                processedIds.append(info.uniqueId);
                qDebug() << QString("成功解析显示器: %1").arg(info.getDisplayString());
            }
        }
    }

    qDebug() << QString("总共解析出 %1 个有效显示器").arg(monitors.size());
#endif

    return monitors;
}

bool EDIDParser::isValidEDID(const QByteArray& edidData)
{
    // EDID数据最小长度为128字节，可能是128、256、384、512字节等（128的倍数）
    if (edidData.size() < 128 || edidData.size() % 128 != 0) {
        qDebug() << "EDID数据长度无效:" << edidData.size() << "字节（应为128的倍数）";
        return false;
    }

    // 检查EDID头部标识 (00 FF FF FF FF FF FF 00)
    const unsigned char expectedHeader[] = {0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00};
    if (edidData.left(8) != QByteArray(reinterpret_cast<const char*>(expectedHeader), 8)) {
        qDebug() << "EDID头部标识无效";
        return false;
    }

    // 验证基础块（前128字节）的校验和
    QByteArray baseBlock = edidData.left(128);
    if (!validateChecksum(baseBlock)) {
        qDebug() << "EDID基础块校验和无效";
        return false;
    }

    // 如果有扩展块，验证每个扩展块
    if (edidData.size() > 128) {
        int extensionCount = edidData.size() / 128 - 1;
        qDebug() << QString("检测到扩展EDID，共%1个扩展块，总长度%2字节")
                    .arg(extensionCount).arg(edidData.size());

        for (int i = 1; i <= extensionCount; i++) {
            QByteArray extensionBlock = edidData.mid(i * 128, 128);
            if (!validateChecksum(extensionBlock)) {
                qDebug() << QString("EDID扩展块%1校验和无效").arg(i);
                return false;
            }
        }
    }

    return true;
}

QString EDIDParser::getEDIDLengthInfo(const QByteArray& edidData)
{
    if (edidData.size() < 128) {
        return QString("无效EDID（长度%1字节，小于最小128字节）").arg(edidData.size());
    }

    if (edidData.size() % 128 != 0) {
        return QString("无效EDID（长度%1字节，不是128的倍数）").arg(edidData.size());
    }

    int blockCount = edidData.size() / 128;
    if (blockCount == 1) {
        return QString("标准EDID（128字节，1个基础块）");
    } else {
        return QString("扩展EDID（%1字节，1个基础块 + %2个扩展块）")
               .arg(edidData.size())
               .arg(blockCount - 1);
    }
}

QString EDIDParser::parseManufacturer(const QByteArray& data)
{
    if (data.size() < 10) return QString();
    
    // 制造商ID在字节8-9
    quint16 manufacturerId = ((quint8)data[8] << 8) | (quint8)data[9];
    
    // 解码3个5位字符 (A-Z映射到1-26)
    char manufacturer[4];
    manufacturer[0] = ((manufacturerId >> 10) & 0x1F) + 'A' - 1;
    manufacturer[1] = ((manufacturerId >> 5) & 0x1F) + 'A' - 1;
    manufacturer[2] = (manufacturerId & 0x1F) + 'A' - 1;
    manufacturer[3] = '\0';
    
    return QString(manufacturer);
}

QString EDIDParser::parseProductCode(const QByteArray& data)
{
    if (data.size() < 12) return QString();
    
    // 产品代码在字节10-11 (小端序)
    quint16 productCode = (quint8)data[10] | ((quint8)data[11] << 8);
    return QString::number(productCode, 16).toUpper();
}

QString EDIDParser::parseSerialNumber(const QByteArray& data)
{
    if (data.size() < 16) return QString();
    
    // 序列号在字节12-15 (小端序)
    quint32 serialNumber = (quint8)data[12] | 
                          ((quint8)data[13] << 8) |
                          ((quint8)data[14] << 16) |
                          ((quint8)data[15] << 24);
    
    return QString::number(serialNumber);
}

QString EDIDParser::parseModelName(const QByteArray& data)
{
    if (data.size() < 128) return QString();
    
    // 在描述符块中查找型号名称 (标识符0xFC)
    for (int i = 54; i < 126; i += 18) {
        if (data[i] == 0x00 && data[i+1] == 0x00 && data[i+2] == 0x00 &&
            (unsigned char)data[i+3] == 0xFC && data[i+4] == 0x00) {
            // 找到型号名称描述符
            QByteArray modelBytes = data.mid(i + 5, 13);
            // 移除尾部的0x0A和空格
            while (!modelBytes.isEmpty() && 
                   (modelBytes.at(modelBytes.size()-1) == 0x0A || 
                    modelBytes.at(modelBytes.size()-1) == ' ')) {
                modelBytes.chop(1);
            }
            return QString::fromLatin1(modelBytes);
        }
    }
    
    return QString("Unknown Model");
}

void EDIDParser::parseScreenSize(const QByteArray& data, int& width, int& height)
{
    if (data.size() < 23) {
        width = height = 0;
        return;
    }
    
    // 屏幕尺寸在字节21-22 (厘米)
    width = (quint8)data[21] * 10;  // 转换为毫米
    height = (quint8)data[22] * 10; // 转换为毫米
}

bool EDIDParser::validateChecksum(const QByteArray& data)
{
    // 校验和验证针对128字节的块
    if (data.size() != 128) {
        qDebug() << "校验和验证：数据块大小不是128字节:" << data.size();
        return false;
    }

    quint8 sum = 0;
    for (int i = 0; i < 128; i++) {
        sum += (quint8)data[i];
    }

    bool isValid = (sum == 0);
    if (!isValid) {
        qDebug() << "校验和验证失败，计算值:" << QString::number(sum, 16);
    }

    return isValid;
}



#ifdef Q_OS_WIN
QList<QByteArray> EDIDParser::getEDIDFromSetupAPI()
{
    QList<QByteArray> edidList;

    // 获取显示器设备信息集
    HDEVINFO deviceInfoSet = SetupDiGetClassDevs(
        &GUID_DEVCLASS_MONITOR,  // 显示器设备类GUID
        nullptr,
        nullptr,
        DIGCF_PRESENT | DIGCF_PROFILE  // 只获取当前存在的设备
    );

    if (deviceInfoSet == INVALID_HANDLE_VALUE) {
        qDebug() << "SetupDiGetClassDevs failed:" << GetLastError();
        return edidList;
    }

    SP_DEVINFO_DATA deviceInfoData;
    deviceInfoData.cbSize = sizeof(SP_DEVINFO_DATA);

    // 枚举所有显示器设备
    for (DWORD deviceIndex = 0;
         SetupDiEnumDeviceInfo(deviceInfoSet, deviceIndex, &deviceInfoData);
         deviceIndex++) {

        // 获取设备实例ID
        WCHAR deviceInstanceId[MAX_PATH];
        if (CM_Get_Device_ID(deviceInfoData.DevInst, deviceInstanceId, MAX_PATH, 0) == CR_SUCCESS) {
            QString instanceId = QString::fromWCharArray(deviceInstanceId);
            qDebug() << "检测到显示器设备:" << instanceId;

            // 尝试直接从设备获取EDID数据
            QString devicePath = getPhysicalDevicePath(instanceId);
            if (!devicePath.isEmpty()) {
                QByteArray edidData = getEDIDFromDevice(devicePath);
                if (!edidData.isEmpty() && isValidEDID(edidData)) {
                    edidList.append(edidData);
                    qDebug() << "通过SetupAPI获取到EDID数据，大小:" << edidData.size();
                }
            }
        }
    }

    SetupDiDestroyDeviceInfoList(deviceInfoSet);
    return edidList;
}

QList<QByteArray> EDIDParser::getEDIDFromDisplayAPI()
{
    QList<QByteArray> edidList;

    // 枚举所有显示设备
    DISPLAY_DEVICE displayDevice;
    displayDevice.cb = sizeof(DISPLAY_DEVICE);

    for (DWORD deviceNum = 0;
         EnumDisplayDevices(nullptr, deviceNum, &displayDevice, 0);
         deviceNum++) {

        // 只处理活动的显示设备
        if (displayDevice.StateFlags & DISPLAY_DEVICE_ACTIVE) {
            QString deviceName = QString::fromWCharArray(displayDevice.DeviceName);
            QString deviceString = QString::fromWCharArray(displayDevice.DeviceString);

            qDebug() << "检测到活动显示设备:" << deviceName << deviceString;

            // 枚举该显示设备上的监视器
            DISPLAY_DEVICE monitorDevice;
            monitorDevice.cb = sizeof(DISPLAY_DEVICE);

            for (DWORD monitorNum = 0;
                 EnumDisplayDevices(displayDevice.DeviceName, monitorNum, &monitorDevice, 0);
                 monitorNum++) {

                QString monitorId = QString::fromWCharArray(monitorDevice.DeviceID);
                qDebug() << "检测到监视器:" << monitorId;

                // 直接从显示器设备获取EDID数据
                QString devicePath = getPhysicalDevicePath(monitorId);
                if (!devicePath.isEmpty()) {
                    QByteArray edidData = getEDIDFromDevice(devicePath);
                    if (!edidData.isEmpty() && isValidEDID(edidData)) {
                        edidList.append(edidData);
                        qDebug() << "通过DisplayAPI获取到EDID数据，大小:" << edidData.size();
                    }
                }
            }
        }
    }

    return edidList;
}

QList<QByteArray> EDIDParser::getEDIDFromWMI()
{
    QList<QByteArray> edidList;

    // WMI方法实现（简化版本，主要用于备用）
    qDebug() << "WMI方法暂未实现，返回空列表";

    return edidList;
}

QByteArray EDIDParser::getEDIDFromDevice(const QString& devicePath)
{
    QByteArray edidData;

    if (devicePath.isEmpty()) {
        return edidData;
    }

    // 打开设备句柄
    HANDLE hDevice = CreateFileA(
        devicePath.toLocal8Bit().data(),
        GENERIC_READ,
        FILE_SHARE_READ | FILE_SHARE_WRITE,
        nullptr,
        OPEN_EXISTING,
        0,
        nullptr
    );

    if (hDevice != INVALID_HANDLE_VALUE) {
        edidData = getEDIDFromDeviceHandle(hDevice);
        CloseHandle(hDevice);
    } else {
        qDebug() << "无法打开设备:" << devicePath << "错误:" << GetLastError();
    }

    return edidData;
}

QByteArray EDIDParser::getEDIDFromDeviceHandle(HANDLE hDevice)
{
    QByteArray edidData;

    // 使用DeviceIoControl获取EDID数据
    BYTE buffer[1024]; // 足够大的缓冲区
    DWORD bytesReturned = 0;

    // 尝试IOCTL_VIDEO_QUERY_DISPLAY_BRIGHTNESS（可能包含EDID信息）
    if (DeviceIoControl(
        hDevice,
        IOCTL_VIDEO_QUERY_DISPLAY_BRIGHTNESS,
        nullptr, 0,
        buffer, sizeof(buffer),
        &bytesReturned,
        nullptr)) {

        if (bytesReturned >= 128) {
            edidData = QByteArray(reinterpret_cast<const char*>(buffer), bytesReturned);
            qDebug() << "通过DeviceIoControl获取到数据，大小:" << bytesReturned;
        }
    }

    return edidData;
}

QStringList EDIDParser::enumerateDisplayDevicePaths()
{
    QStringList devicePaths;

    // 枚举显示器设备路径
    DISPLAY_DEVICE displayDevice;
    displayDevice.cb = sizeof(DISPLAY_DEVICE);

    for (DWORD deviceNum = 0;
         EnumDisplayDevices(nullptr, deviceNum, &displayDevice, 0);
         deviceNum++) {

        if (displayDevice.StateFlags & DISPLAY_DEVICE_ACTIVE) {
            QString deviceName = QString::fromWCharArray(displayDevice.DeviceName);
            devicePaths.append(deviceName);
        }
    }

    return devicePaths;
}

QString EDIDParser::getPhysicalDevicePath(const QString& displayName)
{
    // 简化实现：直接返回显示器名称作为设备路径
    // 在实际应用中，这里需要更复杂的设备路径解析
    if (displayName.startsWith("\\\\")) {
        return displayName;
    }

    // 构建标准设备路径
    return QString("\\\\.\\%1").arg(displayName);
}
#endif
