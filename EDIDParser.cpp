#include "EDIDParser.h"
#include <QDebug>
#include <QCryptographicHash>
#include <QSettings>
#include <QDir>

#ifdef Q_OS_WIN
#include <windows.h>
#include <setupapi.h>
#include <cfgmgr32.h>
#endif

EDIDParser::EDIDParser()
{
}

EDIDParser::~EDIDParser()
{
}

EDIDParser::EDIDInfo EDIDParser::parseEDID(const QByteArray& edidData)
{
    EDIDInfo info;
    
    if (!isValidEDID(edidData)) {
        qDebug() << "无效的EDID数据";
        return info;
    }
    
    info.rawData = edidData;
    info.manufacturer = parseManufacturer(edidData);
    info.productCode = parseProductCode(edidData);
    info.serialNumber = parseSerialNumber(edidData);
    info.modelName = parseModelName(edidData);
    parseScreenSize(edidData, info.width, info.height);
    
    // 生成唯一标识
    info.uniqueId = generateUniqueId(info);
    
    return info;
}

QString EDIDParser::generateUniqueId(const EDIDInfo& edidInfo)
{
    // 使用制造商、产品代码、序列号生成MD5哈希作为唯一标识
    QString combined = QString("%1_%2_%3")
                      .arg(edidInfo.manufacturer)
                      .arg(edidInfo.productCode)
                      .arg(edidInfo.serialNumber);
    
    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(combined.toUtf8());
    return hash.result().toHex().left(16); // 取前16位作为唯一标识
}

QList<EDIDParser::EDIDInfo> EDIDParser::getAllConnectedMonitors()
{
    QList<EDIDInfo> monitors;
    
#ifdef Q_OS_WIN
    QList<QByteArray> edidDataList = getEDIDFromRegistry();
    
    foreach (const QByteArray& edidData, edidDataList) {
        EDIDInfo info = parseEDID(edidData);
        if (info.isValid()) {
            monitors.append(info);
        }
    }
#endif
    
    return monitors;
}

bool EDIDParser::isValidEDID(const QByteArray& edidData)
{
    // EDID数据必须是128字节
    if (edidData.size() != 128) {
        return false;
    }
    
    // 检查EDID头部标识 (00 FF FF FF FF FF FF 00)
    const unsigned char expectedHeader[] = {0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00};
    if (edidData.left(8) != QByteArray(reinterpret_cast<const char*>(expectedHeader), 8)) {
        return false;
    }
    
    // 验证校验和
    return validateChecksum(edidData);
}

QString EDIDParser::parseManufacturer(const QByteArray& data)
{
    if (data.size() < 10) return QString();
    
    // 制造商ID在字节8-9
    quint16 manufacturerId = ((quint8)data[8] << 8) | (quint8)data[9];
    
    // 解码3个5位字符 (A-Z映射到1-26)
    char manufacturer[4];
    manufacturer[0] = ((manufacturerId >> 10) & 0x1F) + 'A' - 1;
    manufacturer[1] = ((manufacturerId >> 5) & 0x1F) + 'A' - 1;
    manufacturer[2] = (manufacturerId & 0x1F) + 'A' - 1;
    manufacturer[3] = '\0';
    
    return QString(manufacturer);
}

QString EDIDParser::parseProductCode(const QByteArray& data)
{
    if (data.size() < 12) return QString();
    
    // 产品代码在字节10-11 (小端序)
    quint16 productCode = (quint8)data[10] | ((quint8)data[11] << 8);
    return QString::number(productCode, 16).toUpper();
}

QString EDIDParser::parseSerialNumber(const QByteArray& data)
{
    if (data.size() < 16) return QString();
    
    // 序列号在字节12-15 (小端序)
    quint32 serialNumber = (quint8)data[12] | 
                          ((quint8)data[13] << 8) |
                          ((quint8)data[14] << 16) |
                          ((quint8)data[15] << 24);
    
    return QString::number(serialNumber);
}

QString EDIDParser::parseModelName(const QByteArray& data)
{
    if (data.size() < 128) return QString();
    
    // 在描述符块中查找型号名称 (标识符0xFC)
    for (int i = 54; i < 126; i += 18) {
        if (data[i] == 0x00 && data[i+1] == 0x00 && data[i+2] == 0x00 &&
            (unsigned char)data[i+3] == 0xFC && data[i+4] == 0x00) {
            // 找到型号名称描述符
            QByteArray modelBytes = data.mid(i + 5, 13);
            // 移除尾部的0x0A和空格
            while (!modelBytes.isEmpty() && 
                   (modelBytes.at(modelBytes.size()-1) == 0x0A || 
                    modelBytes.at(modelBytes.size()-1) == ' ')) {
                modelBytes.chop(1);
            }
            return QString::fromLatin1(modelBytes);
        }
    }
    
    return QString("Unknown Model");
}

void EDIDParser::parseScreenSize(const QByteArray& data, int& width, int& height)
{
    if (data.size() < 23) {
        width = height = 0;
        return;
    }
    
    // 屏幕尺寸在字节21-22 (厘米)
    width = (quint8)data[21] * 10;  // 转换为毫米
    height = (quint8)data[22] * 10; // 转换为毫米
}

bool EDIDParser::validateChecksum(const QByteArray& data)
{
    if (data.size() != 128) return false;
    
    quint8 sum = 0;
    for (int i = 0; i < 128; i++) {
        sum += (quint8)data[i];
    }
    
    return (sum == 0);
}

QList<QByteArray> EDIDParser::getEDIDFromRegistry()
{
    QList<QByteArray> edidList;
    
#ifdef Q_OS_WIN
    // 从Windows注册表读取EDID数据
    QSettings registry("HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Enum\\DISPLAY", 
                      QSettings::NativeFormat);
    
    QStringList displayKeys = registry.childGroups();
    
    foreach (const QString& displayKey, displayKeys) {
        registry.beginGroup(displayKey);
        QStringList deviceKeys = registry.childGroups();
        
        foreach (const QString& deviceKey, deviceKeys) {
            registry.beginGroup(deviceKey);
            QStringList instanceKeys = registry.childGroups();
            
            foreach (const QString& instanceKey, instanceKeys) {
                registry.beginGroup(instanceKey);
                registry.beginGroup("Device Parameters");
                
                QVariant edidVariant = registry.value("EDID");
                if (edidVariant.isValid()) {
                    QByteArray edidData = edidVariant.toByteArray();
                    if (isValidEDID(edidData)) {
                        edidList.append(edidData);
                    }
                }
                
                registry.endGroup(); // Device Parameters
                registry.endGroup(); // instanceKey
            }
            
            registry.endGroup(); // deviceKey
        }
        
        registry.endGroup(); // displayKey
    }
#endif
    
    return edidList;
}
