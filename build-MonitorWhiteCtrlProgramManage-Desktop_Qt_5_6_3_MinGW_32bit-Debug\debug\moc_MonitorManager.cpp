/****************************************************************************
** Meta object code from reading C++ file 'MonitorManager.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.6.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../MonitorManager.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'MonitorManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.6.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
struct qt_meta_stringdata_MonitorManager_t {
    QByteArrayData data[9];
    char stringdata0[141];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_MonitorManager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_MonitorManager_t qt_meta_stringdata_MonitorManager = {
    {
QT_MOC_LITERAL(0, 0, 14), // "MonitorManager"
QT_MOC_LITERAL(1, 15, 16), // "monitorConnected"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 20), // "EDIDParser::EDIDInfo"
QT_MOC_LITERAL(4, 54, 11), // "monitorInfo"
QT_MOC_LITERAL(5, 66, 19), // "monitorDisconnected"
QT_MOC_LITERAL(6, 86, 18), // "monitorListUpdated"
QT_MOC_LITERAL(7, 105, 16), // "whitelistUpdated"
QT_MOC_LITERAL(8, 122, 18) // "checkMonitorStatus"

    },
    "MonitorManager\0monitorConnected\0\0"
    "EDIDParser::EDIDInfo\0monitorInfo\0"
    "monitorDisconnected\0monitorListUpdated\0"
    "whitelistUpdated\0checkMonitorStatus"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_MonitorManager[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       5,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   39,    2, 0x06 /* Public */,
       5,    1,   42,    2, 0x06 /* Public */,
       6,    0,   45,    2, 0x06 /* Public */,
       7,    0,   46,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       8,    0,   47,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,

       0        // eod
};

void MonitorManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        MonitorManager *_t = static_cast<MonitorManager *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->monitorConnected((*reinterpret_cast< const EDIDParser::EDIDInfo(*)>(_a[1]))); break;
        case 1: _t->monitorDisconnected((*reinterpret_cast< const EDIDParser::EDIDInfo(*)>(_a[1]))); break;
        case 2: _t->monitorListUpdated(); break;
        case 3: _t->whitelistUpdated(); break;
        case 4: _t->checkMonitorStatus(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        void **func = reinterpret_cast<void **>(_a[1]);
        {
            typedef void (MonitorManager::*_t)(const EDIDParser::EDIDInfo & );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&MonitorManager::monitorConnected)) {
                *result = 0;
                return;
            }
        }
        {
            typedef void (MonitorManager::*_t)(const EDIDParser::EDIDInfo & );
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&MonitorManager::monitorDisconnected)) {
                *result = 1;
                return;
            }
        }
        {
            typedef void (MonitorManager::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&MonitorManager::monitorListUpdated)) {
                *result = 2;
                return;
            }
        }
        {
            typedef void (MonitorManager::*_t)();
            if (*reinterpret_cast<_t *>(func) == static_cast<_t>(&MonitorManager::whitelistUpdated)) {
                *result = 3;
                return;
            }
        }
    }
}

const QMetaObject MonitorManager::staticMetaObject = {
    { &QObject::staticMetaObject, qt_meta_stringdata_MonitorManager.data,
      qt_meta_data_MonitorManager,  qt_static_metacall, Q_NULLPTR, Q_NULLPTR}
};


const QMetaObject *MonitorManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MonitorManager::qt_metacast(const char *_clname)
{
    if (!_clname) return Q_NULLPTR;
    if (!strcmp(_clname, qt_meta_stringdata_MonitorManager.stringdata0))
        return static_cast<void*>(const_cast< MonitorManager*>(this));
    return QObject::qt_metacast(_clname);
}

int MonitorManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 5;
    }
    return _id;
}

// SIGNAL 0
void MonitorManager::monitorConnected(const EDIDParser::EDIDInfo & _t1)
{
    void *_a[] = { Q_NULLPTR, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void MonitorManager::monitorDisconnected(const EDIDParser::EDIDInfo & _t1)
{
    void *_a[] = { Q_NULLPTR, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void MonitorManager::monitorListUpdated()
{
    QMetaObject::activate(this, &staticMetaObject, 2, Q_NULLPTR);
}

// SIGNAL 3
void MonitorManager::whitelistUpdated()
{
    QMetaObject::activate(this, &staticMetaObject, 3, Q_NULLPTR);
}
QT_END_MOC_NAMESPACE
